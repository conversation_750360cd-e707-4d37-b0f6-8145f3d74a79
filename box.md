# 盲盒详情接口（后台）

## 接口描述
获取指定盲盒的详细信息，包括基本信息和商品配置

## 请求信息
- **URL**: `/mulandoGreateDestiny/v1/admin/box/detail`
- **Method**: `GET`
- **认证**: 需要管理员权限（Global + Admin中间件）

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 盲盒ID |

## 响应示例
**成功响应**:
```json
{
    "error_code": 0,
    "error_msg": "OK",
    "data": {
        "id": 248,
        "title": "春节盲盒",
        "avatar_image": "https://images.wineyun.com/uploads/box/spring.jpg",
        "valid_time_unit": "month",
        "valid_time_num": 7,
        "price": 0,
        "onsale_status": 2,
        "create_num": 20,
        "active_num": 3,
        "get_num": 2,
        "created_time": "2025-06-30 10:53:51",
        "update_time": "2025-07-02 11:51:32",
        "items": [
            {
                "id": 271,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品A",
                        "short_code": "A001",
                        "num": 2
                    },
                    {
                        "name": "商品B",
                        "short_code": "B001",
                        "num": 1
                    }
                ],
                "claimed_count": 2
            },
            {
                "id": 272,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品C",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            },
            {
                "id": 273,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品D",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            },
            {
                "id": 274,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品E",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            },
            {
                "id": 275,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品F",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            },
            {
                "id": 276,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品G",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            },
            {
                "id": 277,
                "box_id": 248,
                "type": 1,
                "items_info": [
                    {
                        "name": "商品H",
                        "short_code": "C001",
                        "num": 3
                    }
                ],
                "claimed_count": 0
            }
        ]
    }
}
```

**失败响应**:
```json
{
    "error_code": 100007,
    "error_msg": "盲盒不存在"
}
```

## 响应参数说明
### 盲盒基本信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 盲盒ID |
| title | string | 盲盒标题 |
| avatar_image | string | 列表图URL |
| valid_time_unit | string | 有效期单位 |
| valid_time_num | int64 | 有效期数量 |
| price | float64 | 售价 |
| onsale_status | int64 | 上架状态(2=在售中,3=已下架) |
| delete_time | int64 | 删除时间(0=未删除) |
| vh_uid | int64 | 操作用户ID |
| vh_vos_name | string | VOS用户姓名 |
| created_time | string | 创建时间 |
| update_time | string | 更新时间 |
| create_num | int64 | 创建数量 |
| active_num | int64 | 激活数量 |
| get_num | int64 | 领取数量 |
| items | array | 商品配置列表 |

### 商品配置信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 商品配置ID |
| box_id | int64 | 盲盒ID |
| type | int64 | 类型(1=随机,2=固定) |
| items_info | array | 商品详情JSON |
| claimed_count | int | 已领取数量 |


## 调用示例
```bash
curl -X GET "http://localhost:8888/mulandoGreateDestiny/v1/admin/box/detail?id=1" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4="
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 100001 | 服务器错误 |
| 100002 | 参数错误 |
| 100003 | 用户不存在 |
| 100005 | 数据库错误 |
| 100007 | 盲盒不存在 |

## 注意事项
1. 返回完整的盲盒信息和商品配置
2. items_info为JSON格式字符串，需要解析
3. 统计数据包括创建、激活、领取数量
4. 只能查看未删除的盲盒
5. 商品配置按ID排序返回
