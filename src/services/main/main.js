import axios from "axios";

function getHotelList(params) {
    // 获取酒店
    return axios({
        url: "/api/mulando_hotel/v3/hotel/list",
        method: "GET",
        params
    });
}
//酒店新增
function addHotel(data) {
    return axios({
        url: "/api/mulando_hotel/v3/hotel/add",
        method: "post",
        data
    });
}
//酒店编辑
function editHotel(data) {
    return axios({
        url: "/api/mulando_hotel/v3/hotel/edit",
        method: "post",
        data
    });
}
// 获取房间列表
function getHotelRoomsList(params) {
    return axios({
        url: "/api/mulando_hotel/v3/hotel_rooms/list",
        method: "GET",
        params
    });
}
function editHotelRoom(data) {
    return axios({
        url: "/api/mulando_hotel/v3/hotel_rooms/edit",
        method: "post",
        data
    });
}
function addHotelRoom(data) {
    return axios({
        url: "/api/mulando_hotel/v3/hotel_rooms/add",
        method: "post",
        data
    });
}

// 获取设施配置列表
function getConfigList(params) {
    return axios({
        url: "/api/mulando_hotel/v3/facility_config/list",
        method: "GET",
        params
    });
}

// 编辑配置
function editFacilityConfig(data) {
    return axios({
        url: "/api/mulando_hotel/v3/facility_config/edit",
        method: "post",
        data
    });
}
// 获取订房记录列表
function getbookingRecordsList(params) {
    return axios({
        url: "/api/mulando_hotel/v3/booking_records/list",
        method: "GET",
        params
    });
}
// 订单操作
function bookingRecordOperate(data) {
    return axios({
        url: "/api/mulando_hotel/v3/booking_records/operate",
        method: "post",
        data
    });
}

// 换房
function changeRoom(data) {
    return axios({
        url: "/api/mulando_hotel/v3/booking_records/change",
        method: "post",
        data
    });
}
// 续房
function extendRoom(data) {
    return axios({
        url: "/api/mulando_hotel/v3/booking_records/extend",
        method: "post",
        data
    });
}
// 订房
function predetermineRoom(data) {
    return axios({
        url: "/api/mulando_hotel/v3/booking_records/add",
        method: "post",
        data
    });
}
function distributorList(params, config = {}) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/list",
        method: "get",
        params,
        ...config
    });
}

// 添加经销商
function addDistributor(data) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/add",
        method: "post",
        data
    });
}

// 获取省市区数据
function getDivisions(params) {
    return axios({
        url: "/api/mulando/interface/mulando/divisions",
        method: "get",
        params
    });
}

// 获取二维码
function getQrcode(params) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/qrcode",
        method: "get",
        params
    });
}

// 切换经销商状态
function changeDistributorStatus(data) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/status",
        method: "post",
        data
    });
}

// 添加获取经销商分类分布的接口
function getDealerCategoryStats() {
    return axios({
        url: "/api/mulando/interface/mulando/stats/dealer_category",
        method: "get"
    });
}

// 获取省份经销商数量Top10
function getDealerTop10ByProvince() {
    return axios({
        url: "/api/mulando/interface/mulando/stats/dealer_top10_by_province",
        method: "get"
    });
}

// 添加编辑经销商接口
function editDistributor(data) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/update",
        method: "post",
        data
    });
}

// 添加实时生成证书图片接口
export function generateCertImage(data) {
    return axios({
        url: "/api/mulando/interface/mulando/cert/image",
        method: "post",
        data,
        params: {
            format: "base64"
        }
    });
}
function createApproval(data) {
    return axios({
        url: "/api/wechat/v3/wecom/approval/create",
        method: "post",
        data
    });
}
function getAdminList(params) {
    return axios({
        url: "/api/authority/v3/admin/list",
        method: "get",
        params
    });
}
function getUserNameByUid(params) {
    return axios({
        url: "/api/wechat/v3/wecom/contacts/userinfo/byuid",
        method: "get",
        params
    });
}

// ==================== 商场管理相关API ====================

// 获取用户列表
function getUserList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/user/list",
        method: "GET",
        params
    });
}

// ==================== 盲盒管理相关API ====================

// 获取盲盒列表
function getBlindBoxList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/list",
        method: "GET",
        params
    });
}

// 创建盲盒
function createBlindBox(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/create",
        method: "POST",
        data
    });
}

// 更新盲盒
function updateBlindBox(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/update",
        method: "POST",
        data
    });
}

// 获取盲盒详情
function getBlindBoxDetail(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/detail",
        method: "GET",
        params
    });
}

// 绑定商品到盲盒
function bindGoodsToBlindBox(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/bind_goods",
        method: "POST",
        data
    });
}

// 设置盲盒上下架状态
function setBlindBoxOnsaleStatus(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/box/set_onsale_status",
        method: "POST",
        data
    });
}

// ==================== 订单管理相关API ====================

// 获取订单列表
function getOrderList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/order/list",
        method: "GET",
        params
    });
}

// 设置用户禁用状态
function setUserDisabled(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/user/set_disabled",
        method: "POST",
        data
    });
}

// 获取提现申请列表
function getWithdrawList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/balance/withdrawalList",
        method: "GET",
        params
    });
}

// 提现审核（通过/驳回）
function withdrawalReview(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/balance/withdrawalReview",
        method: "POST",
        data
    });
}

// 处理提现申请（通过/驳回）- 旧接口，保留兼容性
function processMallWithdraw(data) {
    return axios({
        url: "/api/mall/v3/withdraw/process",
        method: "POST",
        data
    });
}

// 通过提现申请并自动打款 - 旧接口，保留兼容性
function approveWithdrawWithPayment(data) {
    return axios({
        url: "/api/mall/v3/withdraw/approve",
        method: "POST",
        data
    });
}

// ==================== Banner管理相关API ====================

// 获取Banner列表
function getBannerList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/banner/list",
        method: "GET",
        params
    });
}

// ==================== 抽奖活动管理相关API ====================

// 获取抽奖活动列表
function getLotteryActivityList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/lotteryActivity/list",
        method: "GET",
        params
    });
}

// 修改抽奖活动状态
function updateLotteryActivityStatus(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/lotteryActivity/updateStatus",
        method: "POST",
        data
    });
}

// 创建抽奖活动
function createLotteryActivity(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/lotteryActivity/create",
        method: "POST",
        data
    });
}

// 编辑抽奖活动
function updateLotteryActivity(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/lotteryActivity/update",
        method: "POST",
        data
    });
}

// 获取商品列表
function getGoodsList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/goods/list",
        method: "GET",
        params
    });
}

// 创建商品
function createGoods(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/goods/create",
        method: "POST",
        data
    });
}

// 更新商品
function updateGoods(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/goods/update",
        method: "POST",
        data
    });
}

// 设置商品上下架状态
function setGoodsOnsaleStatus(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/goods/set_onsale_status",
        method: "POST",
        data
    });
}

// 复制商品
function copyGoods(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/goods/copy",
        method: "POST",
        data
    });
}

// 根据简码查询商品信息
function queryProductByShortCode(params) {
    return axios({
        url: "/api/wiki/v3/product/query",
        method: "GET",
        params
    });
}

// 获取标签列表
function getLabelList(params) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/label/simple_list",
        method: "GET",
        params
    });
}

// 创建标签
function createLabel(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/label/create",
        method: "POST",
        data
    });
}

// 同步WineNotes文章内容
function convertWineNotes(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/winenotes/convert",
        method: "POST",
        data
    });
}

// 创建Banner
function createBanner(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/banner/create",
        method: "POST",
        data
    });
}

// 编辑Banner
function editBanner(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/banner/update",
        method: "POST",
        data
    });
}

// 删除Banner
function deleteBanner(data) {
    return axios({
        url: "/api/mulandoGreateDestiny/v1/admin/banner/delete",
        method: "POST",
        data
    });
}

// ==================== 木兰朵抽奖管理相关API ====================

// 核销记录列表
function getRedemptionRecords(params) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/prizeRecordList",
        method: "GET",
        params
    });
}

// 发货列表
function getShippingRecords(params) {
    return axios({
        url: "/api/mulandoRedeem/v1/redemptions/admin/redemptionList",
        method: "GET",
        params
    });
}

// 中奖配置相关API
//批次列表
function getBatchConfigs(params) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/batchList",
        method: "GET",
        params
    });
}

//添加批次
function createBatch(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/batchAdd",
        method: "POST",
        data
    });
}
//更新批次
function updateBatch(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/batchUpdate",
        method: "POST",
        data
    });
}
//修改批次（启用/禁用）
function batchChange(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/batchChange",
        method: "POST",
        data
    });
}
//添加奖品
function addPrize(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/prizeConfAdd",
        method: "POST",
        data
    });
}

//修改奖品
function updatePrize(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/prizeConfUpdare",
        method: "POST",
        data
    });
}

//获取批次配置
function getPrizeConfDetail(params) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/prizeConfDetail",
        method: "GET",
        params
    });
}
//更新大区数量
function batchRegionConfUpdare(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/prize/admin/batchRegionConfUpdare",
        method: "POST",
        data
    });
}

// 商家列表
function getMerchantList(params) {
    return axios({
        url: "/api/mulandoRedeem/v1/merchant/admin/merchantList",
        method: "GET",
        params
    });
}

//商家审核
function merchantReview(data) {
    return axios({
        url: "/api/mulandoRedeem/v1/merchant/admin/change",
        method: "POST",
        data
    });
}

export default {
    getUserNameByUid,
    distributorList,
    getHotelList,
    getAdminList,
    createApproval,
    addHotel,
    editHotel,
    getHotelRoomsList,
    getConfigList,
    editHotelRoom,
    addHotelRoom,
    getbookingRecordsList,
    bookingRecordOperate,
    changeRoom,
    extendRoom,
    editFacilityConfig,
    predetermineRoom,
    addDistributor,
    getDivisions,
    getQrcode,
    changeDistributorStatus,
    getDealerCategoryStats,
    getDealerTop10ByProvince,
    editDistributor,
    generateCertImage,
    // 用户管理相关接口
    getUserList,
    setUserDisabled,
    // 提现管理相关接口
    getWithdrawList,
    withdrawalReview,
    processMallWithdraw,
    approveWithdrawWithPayment,
    // Banner管理相关接口
    getBannerList,
    createBanner,
    editBanner,
    deleteBanner,
    // 抽奖活动管理相关接口
    getLotteryActivityList,
    updateLotteryActivityStatus,
    createLotteryActivity,
    updateLotteryActivity,
    // 商品管理相关接口
    getGoodsList,
    createGoods,
    updateGoods,
    setGoodsOnsaleStatus,
    copyGoods,
    queryProductByShortCode,
    getLabelList,
    createLabel,
    convertWineNotes,
    // 盲盒管理相关接口
    getBlindBoxList,
    createBlindBox,
    updateBlindBox,
    getBlindBoxDetail,
    bindGoodsToBlindBox,
    setBlindBoxOnsaleStatus,
    // 订单管理相关接口
    getOrderList,
    // 木兰朵抽奖管理相关接口
    // 核销记录相关
    getRedemptionRecords,

    // 发货管理相关
    getShippingRecords,

    // 中奖配置相关
    getBatchConfigs,
    batchRegionConfUpdare,
    createBatch,
    updateBatch,
    getPrizeConfDetail,
    addPrize,
    updatePrize,
    batchChange,
    // 商家管理相关
    getMerchantList,
    merchantReview
};
