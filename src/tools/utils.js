/**
 * 工具函数集合
 */

/**
 * 手机号脱敏处理
 * @param {string} phone - 手机号
 * @returns {string} 脱敏后的手机号
 */
export function maskPhone(phone) {
    if (!phone) return "";
    const phoneStr = String(phone);
    if (phoneStr.length === 11) {
        return phoneStr.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    }
    return phoneStr;
}

/**
 * 银行卡号脱敏处理
 * @param {string} cardNumber - 银行卡号
 * @returns {string} 脱敏后的银行卡号
 */
export function maskBankCard(cardNumber) {
    if (!cardNumber) return "";
    const cardStr = String(cardNumber);
    if (cardStr.length >= 8) {
        const start = cardStr.substring(0, 4);
        const end = cardStr.substring(cardStr.length - 4);
        const middle = "*".repeat(cardStr.length - 8);
        return `${start}${middle}${end}`;
    }
    return cardStr;
}

/**
 * 姓名脱敏处理
 * @param {string} name - 姓名
 * @returns {string} 脱敏后的姓名
 */
export function maskName(name) {
    if (!name) return "";
    const nameStr = String(name);
    if (nameStr.length === 1) {
        return nameStr;
    } else if (nameStr.length === 2) {
        return nameStr.charAt(0) + "*";
    } else {
        return (
            nameStr.charAt(0) +
            "*".repeat(nameStr.length - 2) +
            nameStr.charAt(nameStr.length - 1)
        );
    }
}

/**
 * 金额格式化
 * @param {number|string} amount - 金额
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的金额
 */
export function formatAmount(amount, decimals = 2) {
    if (amount === null || amount === undefined || amount === "") return "0.00";
    const num = parseFloat(amount);
    if (isNaN(num)) return "0.00";
    return num.toFixed(decimals);
}

/**
 * 时间格式化
 * @param {string|Date} time - 时间
 * @param {string} format - 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间
 */
export function formatTime(time, format = "YYYY-MM-DD HH:mm:ss") {
    if (!time) return "";

    const date = new Date(time);
    if (isNaN(date.getTime())) return "";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return format
        .replace("YYYY", year)
        .replace("MM", month)
        .replace("DD", day)
        .replace("HH", hours)
        .replace("mm", minutes)
        .replace("ss", seconds);
}

/**
 * 提现状态映射
 */
export const WITHDRAW_STATUS = {
    1: { text: "审核中", color: "warning" },
    2: { text: "提现处理中", color: "primary" },
    3: { text: "提现成功", color: "success" },
    4: { text: "提现驳回", color: "danger" }
};

/**
 * 获取提现状态信息
 * @param {number|string} status - 状态值
 * @returns {object} 状态信息 { text, color }
 */
export function getWithdrawStatus(status) {
    return WITHDRAW_STATUS[status] || { text: "未知", color: "info" };
}

/**
 * 操作记录类型映射
 */
export const OPERATION_TYPE = {
    approve: "通过",
    reject: "驳回",
    process: "处理"
};

/**
 * 获取操作记录文本
 * @param {string} type - 操作类型
 * @returns {string} 操作文本
 */
export function getOperationText(type) {
    return OPERATION_TYPE[type] || type;
}
