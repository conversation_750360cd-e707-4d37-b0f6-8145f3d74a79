<template>
   <div>
    <el-form >
        <div style="display: flex; flex-direction: row-reverse;">
            <el-button v-if="!isEdit" type="primary" @click="isEdit = true">编辑</el-button>
            <el-button v-else type="success" @click="saveClick">保存</el-button>
        </div>
        <el-form-item  v-for="(config, index) in facility_config" :key="config.id" :label="config.name" prop="" label-width="120">
                <el-select
                    v-model="relationConfig[index]"
                    multiple
                    filterable
                    :disabled="!isEdit"
                    allow-create
                    style="width: 60%;"
                    default-first-option
                    placeholder="请输入筛选项">
                    <el-option
                    v-for="item in facility_config[index].items"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name">
                    </el-option>
                </el-select>
                </el-form-item>
            <!-- <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item> -->
        </el-form>
   </div>
</template>
<script>
export default {
   
    data() {
        return {
            relationConfig:[
                [],
                [],
                [],
                [],
                []
            ],
            facility_config:[],
            isEdit:false,
        };
    },
    mounted() {
       this.getConfigReq();
       
    },
    methods: {
        async getConfigReq(){
        let res = await this.$request.main.getConfigList();
                if (res.data.error_code == 0) {
                    this.facility_config = res.data.data.list;
                    this.relationConfig = this.facility_config.map(facility => facility.items.map(item => item.name));
                }
        },
        saveClick(){
            let minLength = Math.min(this.facility_config.length, this.relationConfig.length);
  
            for (let i = 0; i < minLength; i++) {
               
               let updatedArray = this.relationConfig[i].map(item => {
                        let foundItem = this.facility_config[i].items.find(a => a.name == item);
                        console.log('eee', foundItem);
                        return foundItem ? foundItem : { id: 0, name: item };
                        });
                this.facility_config[i].items = updatedArray;
             }
            // editFacilityCOnfig
            this.$request.main.editFacilityConfig(
                        this.facility_config
                    ).then(result => {
                        if (result.data.error_code == 0) {
                           this.isEdit = false;
                           this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                            this.getConfigReq();
                        }
                    });
        },
    },

    filters: {
    },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
