<template>
    <div class="goods-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="商品名称">
                        <el-input
                            v-model="searchForm.name"
                            placeholder="商品名称"
                            clearable
                            style="width: 200px;"
                        />
                    </el-form-item>

                    <el-form-item label="商品类型">
                        <el-select
                            v-model="searchForm.goods_type"
                            placeholder="商品类型"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option
                                v-for="item in goodsTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="简码">
                        <el-input
                            v-model="searchForm.short_code"
                            placeholder="简码"
                            clearable
                            style="width: 120px;"
                        />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="状态"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="上架" value="1"></el-option>
                            <el-option label="下架" value="0"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="success" @click="showAddDialog"
                            >新增商品</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column
                        prop="id"
                        label="ID"
                        width="60"
                    ></el-table-column>
                    <el-table-column
                        prop="list_image"
                        label="列表图片"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <el-image
                                style="width: 60px; height: 60px"
                                :src="scope.row.list_image"
                                :preview-src-list="
                                    scope.row.list_image
                                        ? [scope.row.list_image]
                                        : []
                                "
                                fit="cover"
                            >
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                </div>
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="商品名称" width="220">
                        <template slot-scope="scope">
                            <div>
                                <div
                                    style="margin-bottom: 4px; font-weight: 500;"
                                >
                                    {{ scope.row.name }}
                                </div>
                                <div
                                    v-if="scope.row.brief"
                                    style="margin-bottom: 8px; color: #909399; font-size: 12px;"
                                >
                                    {{ scope.row.brief }}
                                </div>
                                <div>
                                    <el-tag
                                        :type="
                                            getGoodsTypeTagType(
                                                scope.row.goods_type
                                            )
                                        "
                                        size="mini"
                                        style="margin-right: 5px;"
                                    >
                                        {{
                                            getGoodsTypeName(
                                                scope.row.goods_type
                                            )
                                        }}
                                    </el-tag>
                                    <el-tag
                                        :type="
                                            scope.row.status == 1
                                                ? 'success'
                                                : 'danger'
                                        "
                                        size="mini"
                                    >
                                        {{
                                            scope.row.status == 1
                                                ? "上架"
                                                : "下架"
                                        }}
                                    </el-tag>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="简码"
                        width="140"
                    ></el-table-column>

                    <el-table-column prop="price" label="价格" width="100">
                        <template slot-scope="scope">
                            ¥{{ scope.row.price }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="stock"
                        label="库存"
                        width="80"
                    ></el-table-column>
                    <el-table-column
                        prop="financial_info"
                        label="财务信息"
                        width="130"
                    >
                        <template slot-scope="scope">
                            <div>
                                <div style="margin-bottom: 4px;">
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >最高礼金：</span
                                    >
                                    <span style="font-size: 12px;"
                                        >¥{{ scope.row.max_gift_amount }}</span
                                    >
                                </div>
                                <div style="margin-bottom: 4px;">
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >核算价格：</span
                                    >
                                    <span style="font-size: 12px;"
                                        >¥{{ scope.row.cost_price }}</span
                                    >
                                </div>
                                <div>
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >返现金额：</span
                                    >
                                    <span style="font-size: 12px;"
                                        >¥{{ scope.row.cashback_amount }}</span
                                    >
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tags" label="标签" width="120">
                        <template slot-scope="scope">
                            <el-tag
                                v-for="tag in scope.row.tags"
                                :key="tag"
                                size="mini"
                                style="margin-right: 5px;"
                            >
                                {{ tag }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="paid_users"
                        label="付款人数"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        prop="operation_info"
                        label="操作信息"
                        width="220"
                    >
                        <template slot-scope="scope">
                            <div>
                                <div style="margin-bottom: 4px;">
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >创建时间：</span
                                    >
                                    <span style="font-size: 12px;">{{
                                        scope.row.created_at
                                    }}</span>
                                </div>
                                <div style="margin-bottom: 4px;">
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >更新时间：</span
                                    >
                                    <span style="font-size: 12px;">{{
                                        scope.row.updated_at
                                    }}</span>
                                </div>
                                <div>
                                    <span
                                        style="color: #909399; font-size: 12px;"
                                        >操作人员：</span
                                    >
                                    <span style="font-size: 12px;">{{
                                        scope.row.operator
                                    }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="210" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="editGoods(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                size="mini"
                                :type="
                                    scope.row.status == 1
                                        ? 'warning'
                                        : 'success'
                                "
                                @click="toggleGoodsStatus(scope.row)"
                            >
                                {{ scope.row.status == 1 ? "下架" : "上架" }}
                            </el-button>
                            <el-button
                                size="mini"
                                type="info"
                                @click="copyGoods(scope.row)"
                            >
                                复制
                            </el-button>
                            <!-- 删除按钮已移除 -->
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 新增/编辑商品对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="1100px"
            :close-on-click-modal="false"
            class="goods-dialog"
        >
            <el-form
                :model="goodsForm"
                :rules="goodsRules"
                ref="goodsForm"
                label-width="100px"
                class="compact-form"
            >
                <!-- 基础信息与标签管理并排 -->
                <el-row :gutter="30">
                    <el-col :span="16">
                        <!-- 基础信息 -->
                        <div class="form-section compact-section">
                            <div class="form-section-title">基础信息</div>
                            <el-form-item label="商品简码" prop="short_code">
                                <el-row :gutter="10">
                                    <el-col :span="18">
                                        <el-input
                                            v-model="goodsForm.short_code"
                                            placeholder="请输入商品简码信息"
                                            size="small"
                                            @input="onShortCodeChange"
                                        ></el-input>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="queryProductInfo"
                                            :loading="queryLoading"
                                            :disabled="!goodsForm.short_code"
                                        >
                                            查询
                                        </el-button>
                                    </el-col>
                                </el-row>
                                <div class="form-tip">
                                    商品简码信息，用于标识商品的详细信息
                                </div>
                            </el-form-item>
                            <el-form-item label="商品名称" prop="name">
                                <el-input
                                    v-model="goodsForm.name"
                                    size="small"
                                    :disabled="nameInputDisabled"
                                    placeholder="请先输入简码并点击查询"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="附加产品"
                                prop="additional_product"
                            >
                                <el-input
                                    v-model="goodsForm.additional_product"
                                    size="small"
                                    placeholder="请输入附加产品信息"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="副标题" prop="brief">
                                <el-input
                                    v-model="goodsForm.brief"
                                    size="small"
                                    placeholder="请输入商品副标题"
                                ></el-input>
                            </el-form-item>
                            <el-row :gutter="15">
                                <el-col :span="12">
                                    <el-form-item
                                        label="商品类型"
                                        prop="goods_type"
                                    >
                                        <el-select
                                            v-model="goodsForm.goods_type"
                                            placeholder="请选择"
                                            style="width: 100%;"
                                            size="small"
                                        >
                                            <el-option
                                                v-for="item in goodsTypeList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="状态" prop="status">
                                        <el-radio-group
                                            v-model="goodsForm.status"
                                            size="small"
                                        >
                                            <el-radio :label="1">上架</el-radio>
                                            <el-radio :label="0">下架</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="15">
                                <el-col :span="12">
                                    <el-form-item label="商品价格" prop="price">
                                        <el-input-number
                                            v-model="goodsForm.price"
                                            :precision="2"
                                            :min="0"
                                            style="width: 140px;"
                                            size="small"
                                        ></el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="库存数量" prop="stock">
                                        <el-input-number
                                            v-model="goodsForm.stock"
                                            :min="0"
                                            style="width: 120px;"
                                            size="small"
                                        ></el-input-number>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 财务信息 -->
                        <div class="form-section compact-section">
                            <div class="form-section-title">财务信息</div>
                            <el-row :gutter="15">
                                <el-col :span="8">
                                    <el-form-item
                                        label="财务核算价"
                                        prop="cost_price"
                                    >
                                        <el-input-number
                                            v-model="goodsForm.cost_price"
                                            :precision="2"
                                            :min="0"
                                            style="width: 130px;"
                                            placeholder="请输入财务核算价"
                                            size="small"
                                        ></el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                        label="返现金额"
                                        prop="cashback_amount"
                                    >
                                        <el-input-number
                                            v-model="goodsForm.cashback_amount"
                                            :precision="0"
                                            :min="0"
                                            style="width: 120px;"
                                            placeholder="请输入返现金额"
                                            size="small"
                                        ></el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="最高礼金额度">
                                        <el-input-number
                                            v-model="goodsForm.max_gift_amount"
                                            :precision="0"
                                            :min="0"
                                            style="width: 120px;"
                                            placeholder="请输入最高可抵用礼金额度"
                                            size="small"
                                        ></el-input-number>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <!-- 标签管理 -->
                        <div class="form-section compact-section">
                            <div class="form-section-title">商品标签</div>
                            <div class="tags-management-vertical">
                                <!-- 已选择标签 -->
                                <div class="selected-tags-panel">
                                    <div class="panel-header">
                                        <i class="el-icon-check"></i>
                                        <span
                                            >已选择 ({{
                                                selectedLabels.length
                                            }})</span
                                        >
                                    </div>
                                    <div class="tags-content">
                                        <el-tag
                                            v-for="tag in selectedLabels"
                                            :key="tag.id || tag.name"
                                            closable
                                            @close="removeSelectedTag(tag)"
                                            size="small"
                                            type="success"
                                            class="selected-tag"
                                        >
                                            {{ tag.name }}
                                        </el-tag>
                                        <div
                                            v-if="selectedLabels.length === 0"
                                            class="empty-state"
                                        >
                                            <i class="el-icon-info"></i>
                                            <span>暂无选择标签</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 已有标签 -->
                                <div class="system-tags-section">
                                    <div class="panel-header">
                                        <i class="el-icon-collection-tag"></i>
                                        <span>已有标签</span>
                                    </div>
                                    <div class="tags-content">
                                        <el-tag
                                            v-for="tag in availableLabels"
                                            :key="tag.id"
                                            :type="
                                                isTagSelected(tag)
                                                    ? 'info'
                                                    : 'primary'
                                            "
                                            size="small"
                                            class="system-tag"
                                            @click="toggleTag(tag)"
                                        >
                                            <i
                                                v-if="isTagSelected(tag)"
                                                class="el-icon-check tag-icon"
                                            ></i>
                                            <i
                                                v-else
                                                class="el-icon-plus tag-icon"
                                            ></i>
                                            {{ tag.name }}
                                        </el-tag>
                                    </div>
                                </div>

                                <!-- 自定义标签 -->
                                <div class="custom-tags-section">
                                    <div class="panel-header">
                                        <i class="el-icon-edit"></i>
                                        <span>自定义标签</span>
                                    </div>
                                    <div class="tags-content">
                                        <div class="custom-input-area">
                                            <el-input
                                                v-if="inputVisible"
                                                v-model="inputValue"
                                                ref="saveTagInput"
                                                size="small"
                                                @keyup.enter.native="
                                                    handleInputConfirm
                                                "
                                                @blur="handleInputConfirm"
                                                placeholder="输入标签名称"
                                                class="custom-input"
                                            >
                                                <el-button
                                                    slot="append"
                                                    icon="el-icon-check"
                                                    :loading="
                                                        createLabelLoading
                                                    "
                                                    @click="handleInputConfirm"
                                                ></el-button>
                                            </el-input>
                                            <el-button
                                                v-else
                                                size="small"
                                                type="dashed"
                                                icon="el-icon-plus"
                                                @click="showInput"
                                                class="add-custom-btn"
                                            >
                                                添加自定义标签
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 商品展示信息 -->
                <div class="form-section compact-section">
                    <div class="form-section-title">商品展示</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="商品图片" prop="imageList">
                                <vos-oss
                                    :key="
                                        `goods-image-${dialogTitle}-${goodsForm.id ||
                                            'new'}`
                                    "
                                    list-type="picture-card"
                                    :showFileList="true"
                                    :dir="goodsImageDir"
                                    :file-list="goodsForm.imageList"
                                    :limit="9"
                                    :fileSize="0.5"
                                    :accept="'image/jpeg,image/jpg,image/png'"
                                >
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                                <div class="upload-tip">
                                    建议尺寸：400x400px，支持JPG、PNG格式，最多上传9张图片，单个文件不超过500KB
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="列表图片" prop="listImageList">
                                <vos-oss
                                    :key="
                                        `list-image-${dialogTitle}-${goodsForm.id ||
                                            'new'}`
                                    "
                                    list-type="picture-card"
                                    :showFileList="true"
                                    :dir="goodsImageDir"
                                    :file-list="goodsForm.listImageList"
                                    :limit="1"
                                    :fileSize="0.5"
                                    :accept="'image/jpeg,image/jpg,image/png'"
                                >
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                                <div class="upload-tip">
                                    建议尺寸：200x200px，支持JPG、PNG格式，单个文件不超过500KB
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item
                        label="商品描述"
                        prop="description"
                        class="description-form-item"
                    >
                        <el-button
                            type="success"
                            size="mini"
                            @click="handleDescriptionAction"
                            class="description-action-btn"
                        >
                            <span class="btn-text-multiline">
                                同步<br />WineNote
                            </span>
                        </el-button>
                        <Tinymce
                            ref="editor"
                            v-model.trim="goodsForm.description"
                            @singleValidate="singleValidate"
                            :height="200"
                        />
                    </el-form-item>
                    <el-form-item
                        label="AI资料"
                        prop="ai_resource"
                        class="ai-material-form-item"
                    >
                        <el-button
                            type="success"
                            size="mini"
                            @click="handleAiMaterialAction"
                            class="ai-material-action-btn"
                        >
                            <span class="btn-text-multiline">
                                同步<br />WineNote
                            </span>
                        </el-button>
                        <el-input
                            v-model="goodsForm.ai_resource"
                            type="textarea"
                            :rows="6"
                            placeholder="请输入AI资料内容"
                        />
                    </el-form-item>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="medium"
                    >取消</el-button
                >
                <el-button type="primary" @click="submitGoods" size="medium"
                    >确定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";
import Tinymce from "@/components/Tinymce";

export default {
    name: "GoodsManage",
    components: {
        vosOss,
        Tinymce
    },
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            dateRange: [],
            goodsImageDir: "vinehoo/mulando-hotel/goods/",
            goodsTypeList: [
                { value: 1, label: "普通商品" },
                { value: 2, label: "抽奖商品" }
            ],
            searchForm: {
                page: 1,
                limit: 10,
                name: "",
                status: "",
                goods_type: "",
                short_code: "",
                start_date: "",
                end_date: ""
            },
            dialogVisible: false,
            dialogTitle: "新增商品",
            isEdit: false,
            inputVisible: false,
            inputValue: "",
            queryLoading: false,
            nameInputDisabled: true,
            // 标签管理相关
            availableLabels: [], // 系统中已存在的标签列表
            selectedLabels: [], // 当前选中的标签（包含ID和名称）
            createLabelLoading: false, // 创建标签的加载状态
            goodsForm: {
                id: "",
                name: "",
                brief: "", // 副标题字段
                additional_product: "", // 附加产品字段
                price: 0,
                stock: 0,
                image: "",
                list_image: "", // 新增列表图片字段
                imageList: [], // vos-oss图片列表
                listImageList: [], // vos-oss列表图片列表
                description: "",
                ai_resource: "", // AI资料字段
                status: 1,
                goods_type: 1, // 1:普通商品 2:抽奖商品
                short_code: "",
                max_gift_amount: 0,
                cost_price: 0,
                cashback_amount: 0,
                tags: []
            },
            goodsRules: {
                name: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur"
                    }
                ],
                brief: [
                    {
                        required: true,
                        message: "请输入副标题",
                        trigger: "blur"
                    }
                ],

                price: [
                    {
                        required: true,
                        message: "请输入商品价格",
                        trigger: "blur"
                    }
                ],
                stock: [
                    {
                        required: true,
                        message: "请输入库存数量",
                        trigger: "blur"
                    }
                ],
                imageList: [
                    {
                        required: true,
                        message: "请上传商品图片",
                        trigger: "change"
                    }
                ],
                listImageList: [
                    {
                        required: true,
                        message: "请上传列表图片",
                        trigger: "change"
                    }
                ],
                goods_type: [
                    {
                        required: true,
                        message: "请选择商品类型",
                        trigger: "change"
                    }
                ],
                short_code: [
                    {
                        required: true,
                        message: "请输入商品简码",
                        trigger: "blur"
                    }
                ],
                cost_price: [
                    {
                        required: true,
                        message: "请输入财务核算价",
                        trigger: "blur"
                    }
                ],
                cashback_amount: [
                    {
                        required: true,
                        message: "请输入返现金额",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getGoodsList();
        this.getLabelList();
    },
    watch: {
        dateRange(val) {
            if (val && val.length === 2) {
                this.searchForm.start_date = val[0];
                this.searchForm.end_date = val[1];
            } else {
                this.searchForm.start_date = "";
                this.searchForm.end_date = "";
            }
        },
        // 监听弹窗关闭，清理富文本编辑器状态
        dialogVisible(val) {
            if (!val) {
                // 弹窗关闭时，重置富文本编辑器状态
                this.$nextTick(() => {
                    if (this.$refs.editor && window.tinymce) {
                        const editor = window.tinymce.get(
                            this.$refs.editor.tinymceId
                        );
                        if (editor) {
                            // 重置编辑器的变化状态，确保下次打开时能正常工作
                            this.$refs.editor.hasChange = false;
                        }
                    }
                });
            }
        }
    },
    methods: {
        // 获取商品列表
        async getGoodsList() {
            this.loading = true;
            try {
                // 过滤空值参数
                const params = {};
                Object.keys(this.searchForm).forEach(key => {
                    if (
                        this.searchForm[key] !== "" &&
                        this.searchForm[key] !== null &&
                        this.searchForm[key] !== undefined
                    ) {
                        // 根据接口文档映射参数名
                        if (key === "name") {
                            params.title = this.searchForm[key];
                        } else if (key === "goods_type") {
                            params.type = this.searchForm[key];
                        } else if (key === "status") {
                            // 前端状态：1=上架，0=下架
                            // 接口状态：2=上架，3=下架
                            params.onsale_status =
                                this.searchForm[key] == 1 ? 2 : 3;
                        } else {
                            params[key] = this.searchForm[key];
                        }
                    }
                });

                console.log("请求参数:", params);
                const res = await this.$request.main.getGoodsList(params);
                console.log("商品列表响应:", res);

                if (res.data.error_code === 0) {
                    // 转换数据格式以适配前端显示
                    this.tableData = res.data.data.list.map(item => {
                        // 解析标签
                        const labels = item.labels
                            ? item.labels.map(label => label.name)
                            : [];

                        return {
                            id: item.id,
                            name: item.title,
                            brief: item.brief || "", // 添加副标题字段
                            price: item.price,
                            stock: item.inventory,
                            paid_users: item.sales_user_num,
                            // 商品图片：保存完整的图片字符串（多图片用逗号分隔）
                            image: item.product_img || "",
                            // 列表图片：使用avatar_image字段
                            list_image: item.avatar_image || "",
                            description: item.detail,
                            ai_resource: item.ai_resource || "", // AI资料字段，暂时从后端获取或设为空
                            status: item.onsale_status === 2 ? 1 : 0, // 转换状态：2=上架->1，3=下架->0
                            goods_type: item.type,
                            short_code: item.items_info || "", // 直接使用items_info字段作为简码
                            max_gift_amount: item.deductible_amount,
                            cost_price: item.erp_amount,
                            cashback_amount: item.cashback_amount,
                            tags: labels,
                            created_at: item.created_time,
                            updated_at: item.update_time,
                            operator: item.vh_vos_name,
                            // 保存原始数据用于编辑
                            _original: item
                        };
                    });
                    this.total = res.data.data.total;
                } else {
                    this.$message.error(
                        res.data.error_msg || "获取商品列表失败"
                    );
                }
            } catch (error) {
                console.error("获取商品列表失败:", error);
                this.$message.error("获取商品列表失败");
            } finally {
                this.loading = false;
            }
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getGoodsList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                name: "",
                status: "",
                goods_type: "",
                short_code: "",
                start_date: "",
                end_date: ""
            };
            this.dateRange = [];
            this.getGoodsList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getGoodsList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getGoodsList();
        },
        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = "新增商品";
            this.isEdit = false;
            this.nameInputDisabled = true; // 重置商品名称输入框状态
            this.goodsForm = {
                id: "",
                name: "",
                brief: "", // 初始化副标题
                additional_product: "", // 初始化附加产品
                price: 0,
                stock: 0,
                image: "",
                list_image: "", // 初始化列表图片
                imageList: [], // 初始化vos-oss图片列表
                listImageList: [], // 初始化vos-oss列表图片列表
                description: "",
                ai_resource: "", // 初始化AI资料
                status: 1,
                goods_type: 1,
                short_code: "",
                max_gift_amount: 0,
                cost_price: 0,
                cashback_amount: 0,
                tags: []
            };
            // 重置标签选择
            this.selectedLabels = [];
            this.createLabelLoading = false;
            // 确保表单验证状态也被重置
            this.$nextTick(() => {
                if (this.$refs.goodsForm) {
                    this.$refs.goodsForm.clearValidate();
                }
                // 强制清空富文本编辑器内容
                if (this.$refs.editor && window.tinymce) {
                    const editor = window.tinymce.get(
                        this.$refs.editor.tinymceId
                    );
                    if (editor) {
                        editor.setContent("");
                        // 重置编辑器的变化状态，确保后续的v-model绑定能正常工作
                        this.$refs.editor.hasChange = false;
                    }
                }
            });
            this.dialogVisible = true;
        },
        // 编辑商品
        editGoods(row) {
            this.dialogTitle = "编辑商品";
            this.isEdit = true;
            this.nameInputDisabled = false;

            // 拆分商品简码和附加商品
            let shortCode = row.short_code || "";
            let additionalProduct = "";

            if (shortCode.includes("+")) {
                const parts = shortCode.split("+");
                shortCode = parts[0]; // 第一个作为商品简码
                additionalProduct = parts.slice(1).join("+"); // 后面的作为附加商品
            }

            this.goodsForm = {
                id: row.id,
                name: row.name,
                brief: row.brief || "",
                additional_product: additionalProduct,
                price: row.price,
                stock: row.stock,
                image: row.image,
                list_image: row.list_image || "",
                imageList: row.image
                    ? row.image.includes(",")
                        ? row.image.split(",")
                        : [row.image]
                    : [],
                listImageList: row.list_image ? [row.list_image] : [],
                description: row.description,
                ai_resource: row.ai_resource || "", // 添加AI资料字段
                status: row.status,
                goods_type: row.goods_type,
                short_code: shortCode,
                max_gift_amount: row.max_gift_amount,
                cost_price: row.cost_price,
                cashback_amount: row.cashback_amount,
                tags: row.tags || []
            };

            // 根据商品的标签设置selectedLabels
            this.selectedLabels = [];
            if (row.tags && row.tags.length > 0) {
                row.tags.forEach(tagName => {
                    // 先查找是否是已有标签
                    const systemTag = this.availableLabels.find(
                        label => label.name === tagName
                    );
                    if (systemTag) {
                        this.selectedLabels.push({
                            id: systemTag.id,
                            name: systemTag.name
                        });
                    } else {
                        // 如果不是已有标签，则作为自定义标签
                        this.selectedLabels.push({
                            id: 0,
                            name: tagName
                        });
                    }
                });
            }

            this.dialogVisible = true;

            // 确保富文本编辑器正确显示编辑内容
            this.$nextTick(() => {
                if (this.$refs.editor && window.tinymce) {
                    const editor = window.tinymce.get(
                        this.$refs.editor.tinymceId
                    );
                    if (editor) {
                        editor.setContent(row.description || "");
                        // 重置编辑器的变化状态，确保后续的v-model绑定能正常工作
                        this.$refs.editor.hasChange = false;
                    }
                }
            });
        },
        // 提交商品信息
        async submitGoods() {
            this.$refs.goodsForm.validate(async valid => {
                if (valid) {
                    try {
                        // 根据file-list设置图片URL，支持多图片
                        if (
                            this.goodsForm.imageList &&
                            this.goodsForm.imageList.length > 0
                        ) {
                            // 多个图片用逗号连接
                            this.goodsForm.image = this.goodsForm.imageList.join(
                                ","
                            );
                        }

                        if (
                            this.goodsForm.listImageList &&
                            this.goodsForm.listImageList.length > 0
                        ) {
                            this.goodsForm.list_image = this.goodsForm.listImageList[0];
                        }
                        if (this.isEdit) {
                            // 编辑商品
                            // 拼接商品简码和附加商品
                            let finalShortCode = this.goodsForm.short_code;
                            if (this.goodsForm.additional_product) {
                                finalShortCode +=
                                    "+" + this.goodsForm.additional_product;
                            }

                            const requestData = {
                                id: this.goodsForm.id,
                                title: this.goodsForm.name,
                                brief: this.goodsForm.brief || "", // 添加副标题字段
                                type: this.goodsForm.goods_type,
                                items_info: finalShortCode,
                                price: this.goodsForm.price,
                                inventory: this.goodsForm.stock,
                                erp_amount: this.goodsForm.cost_price || 0,
                                cashback_amount:
                                    this.goodsForm.cashback_amount || 0,
                                deductible_amount:
                                    this.goodsForm.max_gift_amount || 0,
                                // 商品图片：使用处理好的多图片字符串，删除域名
                                product_img:
                                    this.processImageUrls(
                                        this.goodsForm.image
                                    ) || "",
                                // 列表图片：使用列表图片的第一张（通常只有一张），删除域名
                                avatar_image:
                                    this.goodsForm.listImageList &&
                                    this.goodsForm.listImageList.length > 0
                                        ? this.removeDomainFromUrl(
                                              this.goodsForm.listImageList[0]
                                          )
                                        : "",
                                detail: this.goodsForm.description || "",
                                ai_resource: this.goodsForm.ai_resource || "",
                                onsale_status:
                                    this.goodsForm.status === 1 ? 2 : 3,
                                labels: this.selectedLabels.map(tag => ({
                                    label_id: tag.id || 0,
                                    name: tag.name
                                }))
                            };

                            console.log("编辑商品请求参数:", requestData);
                            const res = await this.$request.main.updateGoods(
                                requestData
                            );
                            console.log("编辑商品响应:", res);

                            if (res.data.error_code === 0) {
                                this.$message.success("编辑成功");
                                this.dialogVisible = false;
                                this.getGoodsList();
                            } else {
                                this.$message.error(
                                    res.data.error_msg || "编辑失败"
                                );
                            }
                        } else {
                            // 新增商品
                            // 拼接商品简码和附加商品
                            let finalShortCode = this.goodsForm.short_code;
                            if (this.goodsForm.additional_product) {
                                finalShortCode +=
                                    "+" + this.goodsForm.additional_product;
                            }

                            const requestData = {
                                title: this.goodsForm.name,
                                brief: this.goodsForm.brief || "", // 添加副标题字段
                                type: this.goodsForm.goods_type,
                                items_info: finalShortCode,
                                price: this.goodsForm.price,
                                inventory: this.goodsForm.stock,
                                erp_amount: this.goodsForm.cost_price || 0,
                                cashback_amount:
                                    this.goodsForm.cashback_amount || 0,
                                deductible_amount:
                                    this.goodsForm.max_gift_amount || 0,
                                // 商品图片：使用处理好的多图片字符串，删除域名
                                ai_resource: this.goodsForm.ai_resource || "",
                                product_img:
                                    this.processImageUrls(
                                        this.goodsForm.image
                                    ) || "",
                                // 列表图片：使用列表图片的第一张（通常只有一张），删除域名
                                avatar_image:
                                    this.goodsForm.listImageList &&
                                    this.goodsForm.listImageList.length > 0
                                        ? this.removeDomainFromUrl(
                                              this.goodsForm.listImageList[0]
                                          )
                                        : "",
                                detail: this.goodsForm.description || "",
                                onsale_status:
                                    this.goodsForm.status === 1 ? 2 : 3, // 前端1=上架->后端2，前端0=下架->后端3
                                labels: this.selectedLabels.map(tag => ({
                                    label_id: tag.id || 0, // 如果是新标签，ID为0
                                    name: tag.name
                                }))
                            };

                            console.log("创建商品请求参数:", requestData);
                            const res = await this.$request.main.createGoods(
                                requestData
                            );
                            console.log("创建商品响应:", res);

                            if (res.data.error_code === 0) {
                                this.$message.success("新增成功");
                                this.dialogVisible = false;
                                this.getGoodsList();
                            } else {
                                this.$message.error(
                                    res.data.error_msg || "新增失败"
                                );
                            }
                        }
                    } catch (error) {
                        console.error("提交商品信息失败:", error);
                        this.$message.error("提交失败，请重试");
                    }
                }
            });
        },
        // 切换商品状态
        async toggleGoodsStatus(row) {
            const action = row.status == 1 ? "下架" : "上架";
            this.$confirm(`确定要${action}该商品吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    // 调用上下架接口
                    // 前端状态：1=上架，0=下架
                    // 接口状态：2=上架，3=下架
                    const newStatus = row.status == 1 ? 3 : 2;
                    const requestData = {
                        id: row.id,
                        onsale_status: newStatus
                    };

                    console.log("商品上下架请求参数:", requestData);
                    const res = await this.$request.main.setGoodsOnsaleStatus(
                        requestData
                    );
                    console.log("商品上下架响应:", res);

                    if (res.data.error_code === 0) {
                        this.$message.success(`${action}成功`);
                        this.getGoodsList();
                    } else {
                        this.$message.error(
                            res.data.error_msg || `${action}失败`
                        );
                    }
                } catch (error) {
                    console.error("商品上下架失败:", error);
                    this.$message.error(`${action}失败，请重试`);
                }
            });
        },

        // 获取商品类型名称
        getGoodsTypeName(type) {
            const typeItem = this.goodsTypeList.find(
                item => item.value === type
            );
            return typeItem ? typeItem.label : "未知类型";
        },
        // 获取商品类型标签类型
        getGoodsTypeTagType(type) {
            const typeMap = {
                1: "primary", // 普通商品
                2: "success" // 抽奖商品
            };
            return typeMap[type] || "info";
        },
        // 获取标签列表
        async getLabelList() {
            try {
                const params = {
                    page: 1
                };
                const res = await this.$request.main.getLabelList(params);
                console.log("标签列表响应:", res);

                if (res.data.error_code === 0) {
                    this.availableLabels = res.data.data.list || [];
                } else {
                    console.error("获取标签列表失败:", res.data.error_msg);
                }
            } catch (error) {
                console.error("获取标签列表失败:", error);
            }
        },

        // 检查标签是否已选择
        isTagSelected(tag) {
            return this.selectedLabels.some(selected => selected.id === tag.id);
        },

        // 切换标签选择状态
        toggleTag(tag) {
            if (this.isTagSelected(tag)) {
                // 如果已选择，则移除
                this.selectedLabels = this.selectedLabels.filter(
                    selected => selected.id !== tag.id
                );
            } else {
                // 如果未选择，则添加
                this.selectedLabels.push({
                    id: tag.id,
                    name: tag.name
                });
            }
            // 同步更新goodsForm.tags用于兼容现有逻辑
            this.goodsForm.tags = this.selectedLabels.map(label => label.name);
        },

        // 移除已选择的标签
        removeSelectedTag(tag) {
            this.selectedLabels = this.selectedLabels.filter(
                selected =>
                    (selected.id || selected.name) !== (tag.id || tag.name)
            );
            // 同步更新goodsForm.tags用于兼容现有逻辑
            this.goodsForm.tags = this.selectedLabels.map(label => label.name);
        },

        // 移除标签（保留原方法用于兼容）
        removeTag(tag) {
            this.goodsForm.tags.splice(this.goodsForm.tags.indexOf(tag), 1);
            // 同步更新selectedLabels
            this.selectedLabels = this.selectedLabels.filter(
                selected => selected.name !== tag
            );
        },
        // 显示输入框
        showInput() {
            this.inputVisible = true;
            this.$nextTick(() => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        // 确认输入标签
        async handleInputConfirm() {
            let inputValue = this.inputValue.trim();
            if (!inputValue) {
                this.inputVisible = false;
                this.inputValue = "";
                return;
            }

            // 检查是否已存在相同名称的标签（在已选择标签和已有标签中）
            const existingInSelected = this.selectedLabels.find(
                tag => tag.name === inputValue
            );
            const existingInSystem = this.availableLabels.find(
                tag => tag.name === inputValue
            );

            if (existingInSelected) {
                this.$message.warning("该标签已在已选择列表中");
                this.inputVisible = false;
                this.inputValue = "";
                return;
            }

            if (existingInSystem) {
                this.$message.warning("该标签已存在于已有标签中，请直接选择");
                this.inputVisible = false;
                this.inputValue = "";
                return;
            }

            this.createLabelLoading = true;
            try {
                // 调用接口创建标签
                const requestData = {
                    name: inputValue
                };

                console.log("创建标签请求参数:", requestData);
                const res = await this.$request.main.createLabel(requestData);
                console.log("创建标签响应:", res);

                if (res.data.error_code === 0) {
                    // 创建成功，获取新标签的ID
                    const newLabelId =
                        res.data.data.id || res.data.data.label_id;

                    // 将新创建的标签添加到已选择列表
                    this.selectedLabels.push({
                        id: newLabelId,
                        name: inputValue
                    });

                    // 将新标签添加到已有标签列表中，以便下次可以直接选择
                    this.availableLabels.push({
                        id: newLabelId,
                        name: inputValue
                    });

                    // 同步更新goodsForm.tags用于兼容现有逻辑
                    this.goodsForm.tags = this.selectedLabels.map(
                        label => label.name
                    );

                    this.$message.success(`标签"${inputValue}"创建成功`);
                } else {
                    this.$message.error(res.data.error_msg || "创建标签失败");
                }
            } catch (error) {
                console.error("创建标签失败:", error);
                this.$message.error("创建标签失败，请重试");
            } finally {
                this.createLabelLoading = false;
            }

            this.inputVisible = false;
            this.inputValue = "";
        },
        // 复制商品
        async copyGoods(row) {
            this.$confirm("确定要复制该商品吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    const requestData = {
                        id: row.id
                    };

                    console.log("复制商品请求参数:", requestData);
                    const res = await this.$request.main.copyGoods(requestData);
                    console.log("复制商品响应:", res);

                    if (res.data.error_code === 0) {
                        this.$message.success("复制成功");
                        this.getGoodsList(); // 刷新页面
                    } else {
                        this.$message.error(res.data.error_msg || "复制失败");
                    }
                } catch (error) {
                    console.error("复制商品失败:", error);
                    this.$message.error("复制失败，请重试");
                }
            });
        },

        // 简码输入变化处理
        onShortCodeChange() {
            // 当简码变化时，重置商品名称输入框状态
            if (!this.goodsForm.short_code) {
                this.nameInputDisabled = true;
                this.goodsForm.name = "";
            }
        },

        // 根据简码查询商品信息
        async queryProductInfo() {
            if (!this.goodsForm.short_code) {
                this.$message.warning("请先输入商品简码");
                return;
            }

            this.queryLoading = true;
            try {
                const params = {
                    short_code: this.goodsForm.short_code
                };

                console.log("查询商品信息请求参数:", params);
                const res = await this.$request.main.queryProductByShortCode(
                    params
                );
                console.log("查询商品信息响应:", res);

                if (
                    res.data.error_code === 0 &&
                    res.data.data &&
                    res.data.data.length > 0
                ) {
                    const productInfo = res.data.data[0];
                    // 设置商品名称为查询到的中文名称
                    this.goodsForm.name = productInfo.cn_product_name || "";
                    // 启用商品名称输入框，允许用户编辑
                    this.nameInputDisabled = false;
                    this.$message.success("查询成功，已自动填入商品名称");
                } else {
                    this.$message.warning("未找到对应的商品信息");
                    this.nameInputDisabled = false; // 即使没找到也允许手动输入
                }
            } catch (error) {
                console.error("查询商品信息失败:", error);
                this.$message.error("查询失败，请重试");
                this.nameInputDisabled = false; // 查询失败也允许手动输入
            } finally {
                this.queryLoading = false;
            }
        },

        // 富文本编辑器验证方法
        singleValidate() {
            // 这个方法用于富文本编辑器的验证回调
            // 可以在这里添加自定义验证逻辑
        },

        // 商品描述操作按钮处理方法
        async handleDescriptionAction() {
            try {
                // 检查富文本编辑器是否已有内容
                let currentContent = "";
                if (this.$refs.editor && window.tinymce) {
                    const editor = window.tinymce.get(
                        this.$refs.editor.tinymceId
                    );
                    if (editor) {
                        currentContent = editor.getContent();
                    }
                }

                // 如果已有内容，先询问用户是否继续
                if (currentContent && currentContent.trim() !== "") {
                    try {
                        await this.$confirm(
                            "当前商品描述已有内容，同步操作将覆盖现有内容，是否继续？",
                            "提示",
                            {
                                confirmButtonText: "继续",
                                cancelButtonText: "取消",
                                type: "warning"
                            }
                        );
                    } catch (error) {
                        // 用户取消操作
                        return;
                    }
                }

                // 创建操作指南的HTML内容
                const guideMessage = `
                    <div class="winenote-guide">
                        <strong>文章获取操作指南：</strong><br>
                        方式一：在Winenotes中复制链接（通过分享功能实现）<br>
                        方式二：在中台的“笔记管理”中点击笔记标题旁的复制按钮
                    </div>
                    <br>
                    请输入文章地址:
                `;

                // 弹出输入框让用户输入文章地址
                const { value: articleUrl } = await this.$prompt(
                    guideMessage,
                    "同步WineNotes",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        inputPlaceholder: "请输入完整的文章链接地址",
                        dangerouslyUseHTMLString: true,
                        inputValidator: value => {
                            if (!value) {
                                return "请输入文章地址";
                            }
                            // 验证是否包含支持的域名
                            const supportedDomains = [
                                "prod-2g5b801oc48c9ceb-1325014929.tcloudbaseapp.com",
                                "vos.vinehoo.com"
                            ];
                            const isValidDomain = supportedDomains.some(
                                domain => value.includes(domain)
                            );
                            if (!isValidDomain) {
                                return "请输入有效的文章地址";
                            }
                            return true;
                        },
                        customClass: "winenote-sync-dialog-simple"
                    }
                );

                // 从URL中提取文章ID
                const articleId = this.extractArticleId(articleUrl);
                if (!articleId) {
                    this.$message.error("无法从URL中提取文章ID");
                    return;
                }

                console.log("提取的文章ID:", articleId);

                // 调用后端接口获取文章内容
                await this.fetchArticleContent(articleId);
            } catch (error) {
                if (error !== "cancel") {
                    console.error("同步WineNote失败:", error);
                    // this.$message.error("同步失败，请重试");
                }
            }
        },

        // 从URL中提取文章ID的方法
        extractArticleId(url) {
            try {
                console.log("开始解析URL:", url);

                // 链接样式1：https://prod-2g5b801oc48c9ceb-1325014929.tcloudbaseapp.com/index.html?cloudMiniPath=pages/detail/detail&id=2499&uuid=1730165242k7oLJa
                if (
                    url.includes(
                        "prod-2g5b801oc48c9ceb-1325014929.tcloudbaseapp.com"
                    )
                ) {
                    console.log("匹配到链接样式1");
                    try {
                        const urlObj = new URL(url);
                        console.log("URL对象:", urlObj);
                        console.log(
                            "所有查询参数:",
                            urlObj.searchParams.toString()
                        );

                        const uuid = urlObj.searchParams.get("uuid");
                        console.log("提取到的uuid:", uuid);

                        if (uuid) {
                            return uuid;
                        } else {
                            console.log("uuid参数为空，尝试手动解析");
                            // 手动解析UUID，防止URL解析问题
                            const uuidMatch = url.match(/[?&]uuid=([^&]+)/);
                            if (uuidMatch && uuidMatch[1]) {
                                console.log("手动解析到的uuid:", uuidMatch[1]);
                                return uuidMatch[1];
                            }
                        }
                    } catch (urlError) {
                        console.error("URL解析失败，尝试手动解析:", urlError);
                        // 手动解析UUID
                        const uuidMatch = url.match(/[?&]uuid=([^&]+)/);
                        if (uuidMatch && uuidMatch[1]) {
                            console.log("手动解析到的uuid:", uuidMatch[1]);
                            return uuidMatch[1];
                        }
                    }
                }

                // 链接样式2：https://vos.vinehoo.com/react-winenotes-backend/note-detail/1745405493INCS4U
                if (url.includes("vos.vinehoo.com")) {
                    console.log("匹配到链接样式2");
                    const parts = url.split("/");
                    const articleId = parts[parts.length - 1];
                    console.log("提取到的articleId:", articleId);
                    if (articleId) {
                        return articleId;
                    }
                }

                console.log("无法从URL中提取文章ID");
                return null;
            } catch (error) {
                console.error("提取文章ID失败:", error);
                return null;
            }
        },

        // 调用后端接口获取文章内容
        async fetchArticleContent(uuid) {
            try {
                const requestData = {
                    uuid: uuid
                };

                console.log("调用接口参数:", requestData);

                // 调用后端接口
                const response = await this.$request.main.convertWineNotes(
                    requestData
                );

                console.log("接口响应:", response);

                if (response.data.error_code === 0) {
                    const htmlContent = response.data.data.html;
                    console.log("获取到的HTML内容:", htmlContent);

                    // 将HTML内容设置到富文本编辑器中
                    if (this.$refs.editor && window.tinymce) {
                        const editor = window.tinymce.get(
                            this.$refs.editor.tinymceId
                        );
                        if (editor) {
                            editor.setContent(htmlContent);
                            this.goodsForm.description = htmlContent;
                        }
                    }

                    this.$message.success("文章内容同步成功");
                }
            } catch (error) {
                console.error("调用接口失败:", error);
                this.$message.error("网络请求失败，请重试");
            }
        },

        // AI资料操作按钮处理方法
        async handleAiMaterialAction() {
            try {
                // 检查AI资料输入框是否已有内容
                let currentContent = this.goodsForm.ai_resource || "";

                // 如果已有内容，先询问用户是否继续
                if (currentContent && currentContent.trim() !== "") {
                    try {
                        await this.$confirm(
                            "当前AI资料已有内容，同步操作将覆盖现有内容，是否继续？",
                            "提示",
                            {
                                confirmButtonText: "继续",
                                cancelButtonText: "取消",
                                type: "warning"
                            }
                        );
                    } catch (error) {
                        // 用户取消操作
                        return;
                    }
                }

                // 创建操作指南的HTML内容
                const guideMessage = `
                    <div class="winenote-guide">
                        <strong>文章获取操作指南：</strong><br>
                        方式一：在Winenotes中复制链接（通过分享功能实现）<br>
                        方式二：在中台的"笔记管理"中点击笔记标题旁的复制按钮
                    </div>
                    <br>
                    请输入文章地址:
                `;

                // 弹出输入框让用户输入文章地址
                const { value: articleUrl } = await this.$prompt(
                    guideMessage,
                    "同步WineNotes到AI资料",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        inputPlaceholder: "请输入完整的文章链接地址",
                        dangerouslyUseHTMLString: true,
                        inputValidator: value => {
                            if (!value) {
                                return "请输入文章地址";
                            }
                            // 验证是否包含支持的域名
                            const supportedDomains = [
                                "prod-2g5b801oc48c9ceb-1325014929.tcloudbaseapp.com",
                                "vos.vinehoo.com"
                            ];
                            const isValidDomain = supportedDomains.some(
                                domain => value.includes(domain)
                            );
                            if (!isValidDomain) {
                                return "请输入有效的文章地址";
                            }
                            return true;
                        },
                        customClass: "winenote-sync-dialog-simple"
                    }
                );

                // 从URL中提取文章ID
                const articleId = this.extractArticleId(articleUrl);
                if (!articleId) {
                    this.$message.error("无法从URL中提取文章ID");
                    return;
                }

                console.log("提取的文章ID:", articleId);

                // 调用后端接口获取文章内容并填充到AI资料
                await this.fetchArticleContentForAiMaterial(articleId);
            } catch (error) {
                if (error !== "cancel") {
                    console.error("同步WineNote到AI资料失败:", error);
                    // this.$message.error("同步失败，请重试");
                }
            }
        },

        // 调用后端接口获取文章内容并填充到AI资料
        async fetchArticleContentForAiMaterial(uuid) {
            try {
                const requestData = {
                    uuid: uuid
                };

                console.log("调用接口参数:", requestData);

                // 调用后端接口
                const response = await this.$request.main.convertWineNotes(
                    requestData
                );

                console.log("接口响应:", response);

                if (response.data.error_code === 0) {
                    const htmlContent = response.data.data.html;
                    console.log("获取到的HTML内容:", htmlContent);

                    // 将HTML内容转换为纯文本并设置到AI资料输入框中
                    const textContent = this.htmlToText(htmlContent);
                    this.goodsForm.ai_resource = textContent;

                    this.$message.success("文章内容同步到AI资料成功");
                }
            } catch (error) {
                console.error("调用接口失败:", error);
                this.$message.error("网络请求失败，请重试");
            }
        },

        // 将HTML内容转换为纯文本
        htmlToText(html) {
            if (!html) return "";

            // 创建一个临时的div元素来解析HTML
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = html;

            // 获取纯文本内容
            let textContent = tempDiv.textContent || tempDiv.innerText || "";

            // 清理多余的空白字符
            textContent = textContent.replace(/\s+/g, " ").trim();

            return textContent;
        },

        // 删除图片URL中的域名部分
        removeDomainFromUrl(url) {
            if (!url) return "";

            try {
                // 如果是完整的URL，提取路径部分
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    const urlObj = new URL(url);
                    // 返回路径部分，保留开头的斜杠
                    return urlObj.pathname;
                }
                // 如果不是完整URL，直接返回
                return url;
            } catch (error) {
                console.warn("URL解析失败:", url, error);
                // 如果解析失败，尝试简单的字符串处理
                const match = url.match(/https?:\/\/[^/]+(\/.+)/);
                return match ? match[1] : url;
            }
        },

        // 处理图片URL列表，删除域名
        processImageUrls(imageStr) {
            if (!imageStr) return "";

            // 如果是多个图片用逗号分隔
            if (imageStr.includes(",")) {
                return imageStr
                    .split(",")
                    .map(url => this.removeDomainFromUrl(url.trim()))
                    .filter(url => url) // 过滤空值
                    .join(",");
            } else {
                return this.removeDomainFromUrl(imageStr);
            }
        },

        // 获取第一张图片（用于表格显示）
        getFirstImage(imageStr) {
            if (!imageStr) return "";
            const images = imageStr.split(",");
            return images[0] || "";
        },

        // 获取图片列表（用于预览）
        getImageList(imageStr) {
            if (!imageStr) return [];
            return imageStr.split(",").filter(img => img.trim());
        },

        // 获取图片数量
        getImageCount(imageStr) {
            if (!imageStr) return 0;
            return imageStr.split(",").filter(img => img.trim()).length;
        }
    }
};
</script>

<style scoped>
.goods-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    background: #fff;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 30px;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.tag-input {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
}

.button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}

.goods-tags .el-tag {
    margin-right: 8px;
    margin-bottom: 8px;
}

.form-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.compact-section {
    margin-bottom: 15px;
    padding: 12px;
    height: auto;
}

.form-section-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 14px;
}

.compact-form .el-form-item {
    margin-bottom: 12px;
}

.compact-form .el-form-item__label {
    font-size: 13px;
    line-height: 28px;
}

.compact-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 120px;
}

.compact-uploader .el-upload:hover {
    border-color: #409eff;
}

.compact-uploader-icon {
    font-size: 24px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
}

.compact-avatar {
    width: 120px;
    height: 120px;
    display: block;
}

/* 原有的tags-container样式已移动到文件末尾 */

.goods-dialog .el-dialog__body {
    padding: 15px 20px;
}

.upload-tip {
    font-size: 11px;
    color: #909399;
    margin-top: 3px;
    line-height: 1.2;
}

.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.4;
    background: #f5f7fa;
    padding: 6px 8px;
    border-radius: 3px;
    border-left: 3px solid #409eff;
}

/* 标签管理样式 - 新布局 */
.tags-management {
    display: flex;
    gap: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafbfc;
    min-height: 160px;
}

/* 垂直标签管理样式 */
.tags-management-vertical {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafbfc;
    min-height: 300px;
}

.selected-tags-panel {
    flex: 1;
    background: #f0f9ff;
    border-radius: 6px 0 0 6px;
    border-right: 1px solid #e4e7ed;
}

.tags-selection-panel {
    flex: 1.5;
    background: #ffffff;
    border-radius: 0 6px 6px 0;
}

/* 垂直布局中的面板样式 */
.tags-management-vertical .selected-tags-panel {
    flex: none;
    background: #f0f9ff;
    border-radius: 6px;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 8px;
}

.tags-management-vertical .system-tags-section,
.tags-management-vertical .custom-tags-section {
    flex: none;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    margin-bottom: 8px;
}

.tags-management-vertical .custom-tags-section {
    margin-bottom: 0;
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 13px;
    font-weight: 500;
    border-radius: 6px 6px 0 0;
}

.selected-tags-panel .panel-header {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.tags-content {
    padding: 12px;
    min-height: 100px;
    max-height: 120px;
    overflow-y: auto;
}

.selected-tag {
    margin: 0 6px 6px 0;
    transition: all 0.3s ease;
}

.selected-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-tag {
    margin: 0 6px 6px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.system-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-icon {
    margin-right: 4px;
    font-size: 12px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 80px;
    color: #c0c4cc;
    font-size: 12px;
}

.empty-state i {
    font-size: 24px;
    margin-bottom: 8px;
}

.system-tags-section {
    margin-bottom: 15px;
}

.custom-tags-section .panel-header {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.custom-input-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.custom-input {
    width: 100%;
}

.add-custom-btn {
    width: 100%;
    border-style: dashed;
    color: #909399;
    background: #f5f7fa;
    border-color: #dcdfe6;
    transition: all 0.3s ease;
}

.add-custom-btn:hover {
    color: #409eff;
    border-color: #409eff;
    background: #ecf5ff;
}

/* 对话框底部按钮样式 */
.dialog-footer {
    text-align: center;
    padding: 20px 0;
}

.dialog-footer .el-button {
    margin: 0 10px;
    /* min-width: 100px; */
    padding: 12px 20px;
}

/* 商品描述区域样式 */
.description-form-item {
    position: relative;
}

.description-action-btn {
    position: absolute;
    top: 40px;
    left: -86px;
    z-index: 10;
    transform: translateY(-2px);
    height: auto;
    min-height: 36px;
    padding: 4px 8px;
    line-height: 1.2;
}

.btn-text-multiline {
    display: inline-block;
    text-align: center;
    line-height: 1.2;
    font-size: 12px;
}

/* AI资料区域样式 */
.ai-material-form-item {
    position: relative;
}

.ai-material-action-btn {
    position: absolute;
    top: 40px;
    left: -86px;
    z-index: 10;
    transform: translateY(-2px);
    height: auto;
    min-height: 36px;
    padding: 4px 8px;
    line-height: 1.2;
}

/* WineNote同步弹窗样式优化 */
.winenote-sync-dialog {
    width: 520px !important;
}

.winenote-sync-dialog .el-message-box__content {
    padding: 20px 24px !important;
}

.winenote-sync-dialog .el-message-box__input {
    margin-top: 16px !important;
}

.winenote-sync-dialog .el-input__inner {
    border-radius: 6px !important;
    border: 1px solid #dcdfe6 !important;
    padding: 12px 15px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.winenote-sync-dialog .el-input__inner:focus {
    border-color: #409eff !important;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
}

.winenote-sync-dialog .el-message-box__btns {
    padding: 16px 24px 20px !important;
    text-align: center !important;
}

.winenote-sync-dialog .el-button {
    padding: 10px 24px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.winenote-sync-dialog .el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
}

.winenote-sync-dialog .el-button--primary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
}

/* 简化版弹窗样式 */
.winenote-sync-dialog-simple {
    width: 480px !important;
}

.winenote-sync-dialog-simple .el-message-box__content {
    padding: 16px 20px !important;
}

.winenote-sync-dialog-simple .el-message-box__input {
    margin-top: 12px !important;
}

/* 操作指南样式 */
.winenote-guide {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #666;
}

.winenote-sync-dialog-simple .el-input__inner {
    padding: 10px 12px !important;
    font-size: 14px !important;
}

.winenote-sync-dialog-simple .el-message-box__btns {
    padding: 12px 20px 16px !important;
    text-align: center !important;
}

.winenote-sync-dialog-simple .el-button {
    padding: 8px 20px !important;
    font-size: 14px !important;
}
</style>
