<template>
    <div class="withdraw-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="提现人姓名">
                        <el-input
                            v-model="searchForm.ali_name"
                            placeholder="提现人姓名"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="提现账号">
                        <el-input
                            v-model="searchForm.ali_account"
                            placeholder="提现账号"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="手机号">
                        <el-input
                            v-model="searchForm.telephone"
                            placeholder="手机号"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="提现状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="提现状态"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="审核中" :value="1"></el-option>
                            <el-option
                                label="提现处理中"
                                :value="2"
                            ></el-option>
                            <el-option label="提现成功" :value="3"></el-option>
                            <el-option label="提现驳回" :value="4"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="申请时间">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column
                        prop="id"
                        label="申请ID"
                        width="80"
                    ></el-table-column>
                    <el-table-column
                        prop="ali_name"
                        label="提现人姓名"
                        width="120"
                    >
                        <template slot-scope="scope">
                            {{ maskName(scope.row.ali_name) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="ali_account"
                        label="提现账号"
                        width="130"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.ali_account }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="telephone"
                        label="手机号"
                        width="130"
                    >
                        <template slot-scope="scope">
                            {{ maskPhone(scope.row.telephone) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="提现金额" width="120">
                        <template slot-scope="scope">
                            <span style="color: #f56c6c; font-weight: bold;"
                                >¥{{ formatAmount(scope.row.amount) }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="create_time"
                        label="申请时间"
                        width="180"
                    >
                        <template slot-scope="scope">
                            {{ formatTime(scope.row.create_time) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="提现状态" width="120">
                        <template slot-scope="scope">
                            <el-tag
                                :type="
                                    getWithdrawStatusInfo(scope.row.status)
                                        .color
                                "
                            >
                                {{
                                    getWithdrawStatusInfo(scope.row.status).text
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="handle_time"
                        label="处理时间"
                        width="180"
                    >
                        <template slot-scope="scope">
                            {{
                                scope.row.handle_time
                                    ? formatTime(scope.row.handle_time)
                                    : "-"
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="back_note"
                        label="驳回原因"
                        width="180"
                        show-overflow-tooltip
                    >
                        <template slot-scope="scope">
                            <span
                                v-if="scope.row.back_note"
                                style="color: #f56c6c;"
                            >
                                {{ scope.row.back_note }}
                            </span>
                            <span v-else style="color: #999;">
                                -
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="170">
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.status == 1"
                                size="mini"
                                type="success"
                                @click="approveWithdraw(scope.row)"
                            >
                                通过
                            </el-button>
                            <el-button
                                v-if="scope.row.status == 1"
                                size="mini"
                                type="danger"
                                @click="rejectWithdraw(scope.row)"
                            >
                                驳回
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 处理提现对话框 -->
        <el-dialog
            :title="processTitle"
            :visible.sync="processDialogVisible"
            width="500px"
        >
            <el-form
                :model="processForm"
                :rules="processRules"
                ref="processForm"
                label-width="100px"
            >
                <el-form-item label="处理结果">
                    <el-tag type="danger">
                        驳回申请
                    </el-tag>
                </el-form-item>
                <el-form-item label="驳回原因" prop="remark">
                    <el-input
                        type="textarea"
                        :rows="4"
                        placeholder="请输入驳回原因"
                        v-model="processForm.remark"
                    >
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="processDialogVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="submitProcess"
                    >确定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    maskPhone,
    maskBankCard,
    maskName,
    formatAmount,
    formatTime,
    getWithdrawStatus,
    getOperationText
} from "@/tools/utils";

export default {
    name: "WithdrawManage",
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            dateRange: [],
            searchForm: {
                page: 1,
                limit: 10,
                ali_name: "",
                ali_account: "",
                telephone: "",
                status: "",
                start_time: "",
                end_time: ""
            },
            processDialogVisible: false,
            processTitle: "",
            processForm: {
                id: "",
                status: 4,
                remark: ""
            },
            processRules: {
                remark: [
                    {
                        required: true,
                        message: "请输入驳回原因",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    watch: {
        dateRange(val) {
            if (val && val.length === 2) {
                this.searchForm.start_time = val[0] + " 00:00:00";
                this.searchForm.end_time = val[1] + " 23:59:59";
            } else {
                this.searchForm.start_time = "";
                this.searchForm.end_time = "";
            }
        }
    },
    mounted() {
        this.getWithdrawList();
    },
    methods: {
        // 获取提现列表
        async getWithdrawList() {
            this.loading = true;
            try {
                // 构建请求参数，过滤空值
                const params = {};
                Object.keys(this.searchForm).forEach(key => {
                    if (
                        this.searchForm[key] !== "" &&
                        this.searchForm[key] !== null &&
                        this.searchForm[key] !== undefined
                    ) {
                        params[key] = this.searchForm[key];
                    }
                });

                const res = await this.$request.main.getWithdrawList(params);
                console.log("提现列表", res);

                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.list || [];
                    this.total = res.data.data.total || 0;
                } else {
                    this.$message.error(
                        res.data.error_msg || "获取提现列表失败"
                    );
                }
            } catch (error) {
                console.error("获取提现列表失败:", error);
                this.$message.error("网络错误，请稍后重试");
            } finally {
                this.loading = false;
            }
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getWithdrawList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                ali_name: "",
                ali_account: "",
                telephone: "",
                status: "",
                start_time: "",
                end_time: ""
            };
            this.dateRange = [];
            this.getWithdrawList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getWithdrawList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getWithdrawList();
        },
        // 通过提现申请
        async approveWithdraw(row) {
            try {
                await this.$confirm(
                    `确定通过 ${this.maskName(
                        row.ali_name
                    )} 的提现申请吗？\n提现金额：¥${this.formatAmount(
                        row.amount
                    )}`,
                    "确认通过",
                    {
                        confirmButtonText: "确定通过",
                        cancelButtonText: "取消",
                        type: "success"
                    }
                );

                this.loading = true;

                try {
                    const res = await this.$request.main.withdrawalReview({
                        id: row.id,
                        status: 2, // 2表示通过
                        back_note: ""
                    });

                    if (res.data.error_code === 0) {
                        this.$message.success("提现申请已通过");
                        this.getWithdrawList();
                    } else {
                        this.$message.error(res.data.error_msg || "审核失败");
                    }
                } catch (error) {
                    console.error("审核失败:", error);
                    this.$message.error("网络错误，请稍后重试");
                } finally {
                    this.loading = false;
                }
            } catch (error) {
                // 用户取消操作
            }
        },

        // 驳回提现申请
        rejectWithdraw(row) {
            this.processTitle = "驳回提现申请";
            this.processForm = {
                id: row.id,
                status: 4,
                remark: ""
            };
            this.processDialogVisible = true;
        },
        // 提交处理（驳回）
        submitProcess() {
            this.$refs.processForm.validate(async valid => {
                if (valid) {
                    this.loading = true;
                    try {
                        const res = await this.$request.main.withdrawalReview({
                            id: this.processForm.id,
                            status: 4, // 4表示驳回
                            back_note: this.processForm.remark
                        });

                        if (res.data.error_code === 0) {
                            this.$message.success("提现申请已驳回");
                            this.processDialogVisible = false;
                            this.getWithdrawList();
                        } else {
                            this.$message.error(
                                res.data.error_msg || "驳回失败"
                            );
                        }
                    } catch (error) {
                        console.error("驳回失败:", error);
                        this.$message.error("网络错误，请稍后重试");
                    } finally {
                        this.loading = false;
                    }
                }
            });
        },

        // 工具函数方法
        maskPhone,
        maskBankCard,
        maskName,
        formatAmount,
        formatTime,
        getOperationText,

        // 获取提现状态信息
        getWithdrawStatusInfo(status) {
            return getWithdrawStatus(status);
        }
    }
};
</script>

<style scoped>
.withdraw-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    background: #fff;
}
</style>
