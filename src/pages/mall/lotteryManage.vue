<template>
    <div class="lottery-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="活动名称">
                        <el-input
                            v-model="searchForm.title"
                            placeholder="活动名称"
                            clearable
                            style="width: 200px;"
                        />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="状态"
                            clearable
                            style="width: 150px;"
                        >
                            <el-option label="禁用" value="0"></el-option>
                            <el-option label="启用" value="1"></el-option>
                            <el-option label="等待开奖" value="2"></el-option>
                            <el-option label="已开奖" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="success" @click="showAddDialog"
                            >新增活动</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column
                        prop="id"
                        label="ID"
                        width="80"
                    ></el-table-column>
                    <el-table-column
                        prop="title"
                        label="活动名称"
                        width="200"
                    ></el-table-column>
                    <el-table-column
                        prop="describe"
                        label="活动描述"
                        width="250"
                        show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                        prop="start_time"
                        label="开始时间"
                        width="180"
                    ></el-table-column>
                    <el-table-column label="关联商品" width="200">
                        <template slot-scope="scope">
                            <div class="goods-info">
                                <img
                                    v-if="scope.row.goods_img"
                                    :src="scope.row.goods_img"
                                    class="goods-thumb"
                                    @error="handleImageError"
                                />
                                <div class="goods-text">
                                    <div class="goods-title">
                                        {{ scope.row.goods_title }}
                                    </div>
                                    <div class="goods-id">
                                        ID: {{ scope.row.goods_id }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="total"
                        label="开奖人数要求"
                        width="120"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.total }}人
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="winner_count"
                        label="中奖人数设置"
                        width="120"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.winner_count }}人
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="participant_count"
                        label="实时参与人数"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <span
                                :style="{
                                    color:
                                        scope.row.participant_count >=
                                        scope.row.total
                                            ? '#67C23A'
                                            : '#F56C6C'
                                }"
                            >
                                {{ scope.row.participant_count }}人
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                size="mini"
                                @click="editLottery(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                v-if="
                                    scope.row.status === 0 ||
                                        scope.row.status === 1
                                "
                                size="mini"
                                :type="
                                    scope.row.status == 1
                                        ? 'warning'
                                        : 'success'
                                "
                                @click="toggleLotteryStatus(scope.row)"
                            >
                                {{ scope.row.status == 1 ? "禁用" : "启用" }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 新增/编辑抽奖活动对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="1000px"
            :close-on-click-modal="false"
            class="lottery-dialog"
            top="5vh"
        >
            <el-form
                :model="lotteryForm"
                :rules="lotteryRules"
                ref="lotteryForm"
                label-width="100px"
                class="lottery-form"
            >
                <!-- 基本信息和开奖设置并排布局 -->
                <el-row :gutter="20" class="equal-height-row">
                    <!-- 左侧：基本信息 -->
                    <el-col :span="14">
                        <div class="form-section compact height-match">
                            <div class="form-section-title">
                                <i class="el-icon-edit"></i>
                                基本信息
                            </div>
                            <div class="basic-info-content">
                                <el-form-item label="活动名称" prop="title">
                                    <el-input
                                        v-model="lotteryForm.title"
                                        placeholder="请输入活动名称"
                                        maxlength="50"
                                        show-word-limit
                                        size="small"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="活动描述" prop="describe">
                                    <el-input
                                        type="textarea"
                                        :rows="2"
                                        placeholder="请输入活动描述"
                                        v-model="lotteryForm.describe"
                                        maxlength="200"
                                        show-word-limit
                                        size="small"
                                    >
                                    </el-input>
                                </el-form-item>
                                <el-form-item
                                    label="开始时间"
                                    prop="start_time"
                                >
                                    <el-date-picker
                                        v-model="lotteryForm.start_time"
                                        type="datetime"
                                        placeholder="选择活动开始时间"
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        style="width: 100%;"
                                        size="small"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                        </div>
                    </el-col>

                    <!-- 右侧：开奖设置 -->
                    <el-col :span="10">
                        <div class="form-section compact height-match">
                            <div class="form-section-title">
                                <i class="el-icon-trophy"></i>
                                开奖设置
                            </div>
                            <div class="lottery-settings-content">
                                <el-form-item label="开奖门槛" prop="total">
                                    <el-input-number
                                        v-model="lotteryForm.total"
                                        :min="1"
                                        :max="9999"
                                        style="width: 100%;"
                                        controls-position="right"
                                        size="small"
                                    ></el-input-number>
                                    <div class="form-tip">
                                        参与人数达到此门槛才能开奖
                                    </div>
                                </el-form-item>
                                <el-form-item
                                    label="中奖配额"
                                    prop="winner_count"
                                >
                                    <el-input-number
                                        v-model="lotteryForm.winner_count"
                                        :min="1"
                                        :max="lotteryForm.total || 999"
                                        style="width: 100%;"
                                        controls-position="right"
                                        size="small"
                                    ></el-input-number>
                                    <div class="form-tip">最多可中奖的人数</div>
                                </el-form-item>
                                <div
                                    v-if="
                                        lotteryForm.total &&
                                            lotteryForm.winner_count
                                    "
                                    class="probability-info"
                                >
                                    <el-tag type="info" size="mini">
                                        中奖率：{{
                                            (
                                                (lotteryForm.winner_count /
                                                    lotteryForm.total) *
                                                100
                                            ).toFixed(1)
                                        }}%
                                    </el-tag>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 商品关联区域 -->
                <div class="form-section compact goods-section">
                    <div class="form-section-title">
                        <i class="el-icon-goods"></i>
                        商品关联
                    </div>
                    <el-form-item label="关联商品" prop="goods_id">
                        <el-select
                            v-model="lotteryForm.goods_id"
                            placeholder="请选择关联商品"
                            @change="onGoodsChange"
                            filterable
                            clearable
                            size="small"
                        >
                            <el-option
                                v-for="item in goodsList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                                {{ item.name }}
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 商品信息展示（换行布局） -->
                    <div
                        v-if="lotteryForm.goods_id"
                        class="goods-info-section compact"
                    >
                        <!-- 商品标题 -->
                        <el-form-item label="商品标题" label-width="70px">
                            <el-input
                                v-model="lotteryForm.goods_title"
                                placeholder="支持手动编辑"
                                maxlength="100"
                                size="small"
                            ></el-input>
                        </el-form-item>

                        <!-- 商品图片（换行显示，不允许上传） -->
                        <el-form-item label="商品图片" label-width="70px">
                            <div class="goods-image-display">
                                <img
                                    v-if="lotteryForm.goods_img"
                                    :src="lotteryForm.goods_img"
                                    class="goods-image-readonly"
                                    alt="商品图片"
                                />
                                <div v-else class="no-image-placeholder">
                                    <i class="el-icon-picture"></i>
                                    <span>暂无图片</span>
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="medium">
                    <i class="el-icon-close"></i>
                    取消
                </el-button>
                <el-button type="primary" @click="submitLottery" size="medium">
                    <i class="el-icon-check"></i>
                    {{ isEdit ? "更新活动" : "创建活动" }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "LotteryManage",
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            searchForm: {
                page: 1,
                limit: 10,
                title: "",
                status: ""
            },
            dialogVisible: false,
            dialogTitle: "新增抽奖活动",
            isEdit: false,
            goodsList: [], // 商品列表
            lotteryForm: {
                id: "",
                title: "", // API字段名
                describe: "", // API字段名
                start_time: "",
                status: 0, // 默认状态为禁用
                // 商品相关字段
                goods_id: "", // 关联商品ID
                goods_title: "", // 活动商品标题
                goods_img: "", // 活动商品图片
                total: 10, // 开奖人数门槛
                winner_count: 1 // 中奖人数配额，默认为1
            },
            lotteryRules: {
                title: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    }
                ],
                describe: [
                    {
                        required: true,
                        message: "请输入活动描述",
                        trigger: "blur"
                    }
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择开始时间",
                        trigger: "change"
                    }
                ],
                goods_id: [
                    {
                        required: true,
                        message: "请选择关联商品",
                        trigger: "change"
                    }
                ],
                total: [
                    {
                        required: true,
                        message: "请设置开奖人数门槛",
                        trigger: "blur"
                    }
                ],
                winner_count: [
                    {
                        required: true,
                        message: "请设置中奖人数配额",
                        trigger: "blur"
                    }
                ]
            },
            // 模拟数据
            mockData: [
                {
                    id: 1,
                    name: "春节大抽奖",
                    description:
                        "春节期间特别推出的抽奖活动，奖品丰富，欢迎参与！",
                    start_time: "2024-02-01 00:00:00",
                    participate_count: 120,
                    status: 1,
                    // 新增字段
                    goods_id: 1,
                    goods_short_code: "IP15P",
                    goods_title: "iPhone 15 Pro 春节特惠版",
                    goods_image: "https://via.placeholder.com/200x200",
                    min_participants: 100,
                    max_winners: 1
                },
                {
                    id: 2,
                    name: "会员专享抽奖",
                    description: "仅限VIP会员参与的专属抽奖活动",
                    start_time: "2024-01-15 00:00:00",
                    participate_count: 60,
                    status: 1,
                    // 新增字段
                    goods_id: 2,
                    goods_short_code: "LUXPKG",
                    goods_title: "豪华旅游套餐会员专享",
                    goods_image: "https://via.placeholder.com/200x200",
                    min_participants: 50,
                    max_winners: 1
                },
                {
                    id: 3,
                    name: "新用户注册礼",
                    description: "新用户注册即可参与的抽奖活动",
                    start_time: "2023-12-01 00:00:00",
                    participate_count: 250,
                    status: 0,
                    // 新增字段
                    goods_id: 3,
                    goods_short_code: "XMPHONE",
                    goods_title: "小米手机新用户专享",
                    goods_image: "https://via.placeholder.com/200x200",
                    min_participants: 200,
                    max_winners: 1
                }
            ],
            // 商品列表模拟数据
            mockGoodsList: [
                {
                    id: 1,
                    name: "iPhone 15 Pro",
                    short_code: "IP15P",
                    image: "https://via.placeholder.com/200x200"
                },
                {
                    id: 2,
                    name: "豪华旅游套餐",
                    short_code: "LUXPKG",
                    image: "https://via.placeholder.com/200x200"
                },
                {
                    id: 3,
                    name: "小米手机",
                    short_code: "XMPHONE",
                    image: "https://via.placeholder.com/200x200"
                },
                {
                    id: 4,
                    name: "MacBook Pro",
                    short_code: "MBPRO",
                    image: "https://via.placeholder.com/200x200"
                },
                {
                    id: 5,
                    name: "华为Mate60",
                    short_code: "HWM60",
                    image: "https://via.placeholder.com/200x200"
                }
            ]
        };
    },
    mounted() {
        this.getLotteryList();
        this.getGoodsList();
    },
    methods: {
        // 获取状态类型
        getStatusType(status) {
            const statusMap = {
                0: "danger", // 禁用
                1: "success", // 启用
                2: "warning", // 等待开奖
                3: "info" // 已开奖
            };
            return statusMap[status] || "info";
        },
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                0: "禁用",
                1: "启用",
                2: "等待开奖",
                3: "已开奖"
            };
            return statusMap[status] || "未知";
        },
        // 获取抽奖活动列表
        async getLotteryList() {
            this.loading = true;
            try {
                // 构建请求参数
                const params = {
                    page: this.searchForm.page,
                    limit: this.searchForm.limit
                };

                // 添加可选参数
                if (this.searchForm.title) {
                    params.title = this.searchForm.title;
                }
                if (this.searchForm.status !== "") {
                    params.status = this.searchForm.status;
                }

                const response = await this.$request.main.getLotteryActivityList(
                    params
                );

                if (response.data.error_code === 0) {
                    this.tableData = response.data.data.list;
                    this.total = response.data.data.total;
                } else {
                    this.tableData = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("获取抽奖活动列表失败:", error);
                this.$message.error("获取抽奖活动列表失败");
                this.tableData = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        // 获取商品列表
        async getGoodsList() {
            try {
                // 根据API文档要求的参数
                const params = {
                    page: 1,
                    limit: 999,
                    type: 2, // 抽奖商品
                    onsale_status: 2 // 上架状态
                };

                console.log("调用商品列表API，参数:", params);
                console.log("API函数:", this.$request.main.getGoodsList);
                const response = await this.$request.main.getGoodsList(params);
                console.log("API响应:", response);

                if (response.data.error_code === 0) {
                    // 转换数据格式以适配现有的选择器
                    this.goodsList = response.data.data.list.map(item => ({
                        id: item.id,
                        name: item.title,
                        image:
                            item.avatar_image ||
                            (item.product_img
                                ? item.product_img.split(",")[0]
                                : ""),
                        price: item.price,
                        inventory: item.inventory
                    }));
                } else {
                    this.goodsList = [];
                }
            } catch (error) {
                console.error("获取商品列表失败:", error);
                this.goodsList = [];
            }
        },
        // 图片加载错误处理
        handleImageError(event) {
            event.target.style.display = "none";
        },

        // 去除图片URL中的域名
        removeImageDomain(imageUrl) {
            if (!imageUrl) return "";

            // 如果是完整的URL，提取路径部分
            try {
                const url = new URL(imageUrl);
                return url.pathname;
            } catch (e) {
                // 如果不是完整URL，直接返回
                return imageUrl;
            }
        },

        // 商品选择变化
        onGoodsChange(goodsId) {
            const selectedGoods = this.goodsList.find(
                item => item.id === goodsId
            );
            if (selectedGoods) {
                // 自动填充商品信息
                this.lotteryForm.goods_title = selectedGoods.name;
                this.lotteryForm.goods_img = selectedGoods.image;
            }
        },

        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getLotteryList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                title: "",
                status: ""
            };
            this.getLotteryList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getLotteryList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getLotteryList();
        },
        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = "新增抽奖活动";
            this.isEdit = false;
            this.lotteryForm = {
                id: "",
                title: "",
                describe: "",
                start_time: "",
                status: 0, // 默认状态为禁用
                // 商品相关字段
                goods_id: "",
                goods_title: "",
                goods_img: "",
                total: 10,
                winner_count: 1
            };
            this.dialogVisible = true;
        },
        // 编辑抽奖活动
        editLottery(row) {
            this.dialogTitle = "编辑抽奖活动";
            this.isEdit = true;
            this.lotteryForm = {
                id: row.id,
                title: row.title,
                describe: row.describe,
                start_time: row.start_time,
                status: row.status,
                // 商品相关字段
                goods_id: row.goods_id || "",
                goods_title: row.goods_title || "",
                goods_img: row.goods_img || "",
                total: row.total || 10,
                winner_count: row.winner_count || 1
            };
            this.dialogVisible = true;
        },
        // 提交抽奖活动信息
        submitLottery() {
            this.$refs.lotteryForm.validate(async valid => {
                if (valid) {
                    // 验证商品关联
                    if (!this.lotteryForm.goods_id) {
                        this.$message.error("请选择关联商品");
                        return;
                    }

                    // 验证开奖设置
                    if (
                        this.lotteryForm.winner_count > this.lotteryForm.total
                    ) {
                        this.$message.error("中奖人数不能超过开奖人数门槛");
                        return;
                    }

                    try {
                        // 构建API请求数据
                        const requestData = {
                            title: this.lotteryForm.title,
                            describe: this.lotteryForm.describe,
                            status: this.lotteryForm.status,
                            start_time: this.lotteryForm.start_time,
                            goods_id: this.lotteryForm.goods_id,
                            goods_title: this.lotteryForm.goods_title,
                            goods_img: this.removeImageDomain(
                                this.lotteryForm.goods_img
                            ),
                            total: this.lotteryForm.total,
                            winner_count: this.lotteryForm.winner_count
                        };

                        // 如果是编辑模式，添加id参数
                        if (this.isEdit) {
                            requestData.id = this.lotteryForm.id;
                        }

                        // 根据是否为编辑模式调用不同的API
                        const response = this.isEdit
                            ? await this.$request.main.updateLotteryActivity(
                                  requestData
                              )
                            : await this.$request.main.createLotteryActivity(
                                  requestData
                              );

                        if (response.data.error_code === 0) {
                            this.$message.success(
                                this.isEdit ? "编辑成功" : "新增成功"
                            );
                            this.dialogVisible = false;
                            this.getLotteryList();
                        }
                    } catch (error) {
                        console.error("提交抽奖活动失败:", error);
                        this.$message.error("操作失败");
                    }
                }
            });
        },
        // 切换抽奖活动状态
        toggleLotteryStatus(row) {
            const action = row.status == 1 ? "禁用" : "启用";
            const newStatus = row.status == 1 ? 0 : 1;

            this.$confirm(`确定要${action}该抽奖活动吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    try {
                        const response = await this.$request.main.updateLotteryActivityStatus(
                            {
                                id: row.id,
                                status: newStatus
                            }
                        );

                        if (response.data.error_code === 0) {
                            this.$message.success(`${action}成功`);
                            // 刷新列表
                            this.getLotteryList();
                        }
                    } catch (error) {
                        console.error("修改状态失败:", error);
                        this.$message.error(`${action}失败`);
                    }
                })
                .catch(() => {
                    // 用户取消操作
                });
        }
    }
};
</script>

<style scoped>
.lottery-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    background: #fff;
}

/* 商品信息样式 */
.goods-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.goods-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e8eaec;
}

.goods-text {
    flex: 1;
    min-width: 0;
}

.goods-title {
    font-size: 12px;
    color: #2c3e50;
    font-weight: 500;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.goods-id {
    font-size: 11px;
    color: #909399;
    margin-top: 2px;
}

/* 状态提示样式 */
.status-tip {
    font-size: 12px;
    color: #909399;
    font-style: italic;
}

/* 对话框样式优化 */
.lottery-dialog {
    border-radius: 8px;
}

.lottery-dialog .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 20px 24px;
}

.lottery-dialog .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.lottery-dialog .el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 20px;
}

.lottery-dialog .el-dialog__body {
    padding: 12px;
    background: #fafbfc;
    max-height: 80vh;
    overflow-y: auto;
}

/* 表单样式优化 */
.lottery-form {
    background: white;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

.form-section {
    margin-bottom: 12px;
    background: white;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e8eaec;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.form-section.compact {
    padding: 10px;
    margin-bottom: 10px;
}

/* 等高布局 */
.equal-height-row {
    display: flex;
    align-items: stretch;
}

.equal-height-row .el-col {
    display: flex;
}

.form-section.height-match {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 200px;
}

.basic-info-content,
.lottery-settings-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.form-section.goods-section {
    margin-top: 20px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid #e8eaec;
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-right: 5px;
    color: #409eff;
    font-size: 15px;
}

/* 提示文字样式 */
.form-tip {
    color: #909399;
    font-size: 11px;
    margin-top: 2px;
    line-height: 1.2;
}

.form-tip i {
    margin-right: 3px;
    font-size: 12px;
}

/* 中奖概率信息 */
.probability-info {
    margin-top: 6px;
    text-align: center;
}

/* 商品选择容器样式 */
.goods-select-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.goods-select {
    flex: 1;
}

.refresh-btn {
    flex-shrink: 0;
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 商品信息区域样式 */
.goods-info-section {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-top: 6px;
    border: 1px solid #e9ecef;
    position: relative;
}

.goods-info-section.compact {
    padding: 8px;
    margin-top: 4px;
}

.goods-info-header {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.goods-info-header i {
    margin-right: 3px;
    color: #28a745;
    font-size: 13px;
}

/* 商品图片只读显示样式 */
.goods-image-display {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.goods-image-readonly {
    width: 80px;
    height: 80px;
    display: block;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e8eaec;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-image-placeholder {
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
    color: #8c939d;
}

.no-image-placeholder i {
    font-size: 24px;
    margin-bottom: 4px;
}

.no-image-placeholder span {
    font-size: 12px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
    text-align: right;
    padding: 12px 16px;
    background: #fafbfc;
    border-top: 1px solid #e8eaec;
    border-radius: 0 0 6px 6px;
}

.dialog-footer .el-button {
    margin-left: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
}

/* 表单项样式优化 */
.lottery-form .el-form-item {
    margin-bottom: 10px;
}

.lottery-form .el-form-item:last-child {
    margin-bottom: 0;
}

.lottery-form .el-form-item__label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 13px;
    line-height: 1.4;
}

.lottery-form .el-input__inner,
.lottery-form .el-textarea__inner,
.lottery-form .el-select .el-input__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s ease;
}

.lottery-form .el-input__inner:focus,
.lottery-form .el-textarea__inner:focus,
.lottery-form .el-select .el-input__inner:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 数字输入框样式 */
.lottery-form .el-input-number {
    width: 100%;
}

.lottery-form .el-input-number .el-input__inner {
    text-align: left;
}

/* 单选按钮组样式 */
.lottery-form .el-radio-group .el-radio {
    margin-right: 20px;
}

.lottery-form .el-radio__label {
    padding-left: 8px;
}

/* 选择器选项样式 */
/* 警告提示样式 */
.el-alert {
    border-radius: 6px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .lottery-dialog {
        width: 95% !important;
        margin: 0 auto;
    }

    .form-section {
        padding: 16px;
    }

    .lottery-form {
        padding: 16px;
    }

    .goods-image-readonly {
        width: 80px;
        height: 80px;
    }

    .no-image-placeholder {
        width: 80px;
        height: 80px;
    }
}
</style>
