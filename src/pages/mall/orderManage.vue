<template>
    <div class="order-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="子订单号">
                        <el-input
                            v-model="searchForm.sub_order_no"
                            placeholder="子订单号"
                            clearable
                            style="width: 200px;"
                        />
                    </el-form-item>
                    <el-form-item label="商品名称">
                        <el-input
                            v-model="searchForm.goods_name"
                            placeholder="商品名称"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="收件人姓名">
                        <el-input
                            v-model="searchForm.consignee_name"
                            placeholder="收件人姓名"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="收件人手机">
                        <el-input
                            v-model="searchForm.consignee_phone"
                            placeholder="收件人手机号"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="订单状态">
                        <el-select
                            v-model="searchForm.sub_order_status"
                            placeholder="订单状态"
                            clearable
                            filterable
                            style="width: 120px;"
                        >
                            <el-option label="待支付" value="0"></el-option>
                            <el-option label="已支付" value="1"></el-option>
                            <el-option label="已发货" value="2"></el-option>
                            <el-option label="已完成" value="3"></el-option>
                            <el-option label="已取消" value="4"></el-option>
                            <el-option label="已退款" value="5"></el-option>
                            <el-option label="退款中" value="6"></el-option>
                            <el-option label="退款失败" value="7"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="ERP推送状态">
                        <el-select
                            v-model="searchForm.push_t_status"
                            placeholder="ERP推送状态"
                            clearable
                            style="width: 140px;"
                        >
                            <el-option label="未推送" value="0"></el-option>
                            <el-option label="推送成功" value="1"></el-option>
                            <el-option label="推送失败" value="2"></el-option>
                            <el-option label="不推送" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="商品类型">
                        <el-select
                            v-model="searchForm.goods_type"
                            placeholder="商品类型"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="普通商品" value="1"></el-option>
                            <el-option label="盲盒商品" value="3"></el-option>
                            <el-option label="抽奖商品" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="盲盒筛选">
                        <el-select
                            v-model="searchForm.box_id"
                            placeholder="选择盲盒"
                            clearable
                            style="width: 150px;"
                            :disabled="searchForm.goods_type !== '3'"
                        >
                            <el-option
                                v-for="blindBox in blindBoxOptions"
                                :key="blindBox.id"
                                :label="blindBox.title"
                                :value="blindBox.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="支付时间">
                        <el-date-picker
                            v-model="paymentDateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column label="订单信息" width="240">
                        <template slot-scope="scope">
                            <div class="order-info">
                                <div class="order-no">
                                    {{ scope.row.sub_order_no }}
                                </div>
                                <div class="status-tags">
                                    <el-tag
                                        size="mini"
                                        :type="
                                            getStatusColor(
                                                scope.row.sub_order_status
                                            )
                                        "
                                        style="margin-right: 4px;"
                                    >
                                        {{
                                            getStatusName(
                                                scope.row.sub_order_status
                                            )
                                        }}
                                    </el-tag>
                                    <el-tag
                                        v-if="
                                            scope.row.refund_status &&
                                                scope.row.refund_status !== 0
                                        "
                                        size="mini"
                                        :type="
                                            getRefundStatusColor(
                                                scope.row.refund_status
                                            )
                                        "
                                        style="margin-right: 4px;"
                                    >
                                        {{
                                            getRefundStatusName(
                                                scope.row.refund_status
                                            )
                                        }}
                                    </el-tag>

                                    <el-tag
                                        size="mini"
                                        :type="
                                            getErpStatusColor(
                                                scope.row.push_t_status
                                            )
                                        "
                                    >
                                        {{
                                            getErpStatusName(
                                                scope.row.push_t_status
                                            )
                                        }}
                                    </el-tag>
                                </div>
                                <div class="payment-info">
                                    <div class="payment-item-inline">
                                        <span class="payment-label">支付:</span>
                                        <span class="payment-amount"
                                            >¥{{
                                                scope.row.payment_amount
                                            }}</span
                                        >
                                        <span
                                            class="payment-label"
                                            style="margin-left: 12px;"
                                            >礼金:</span
                                        >
                                        <span
                                            class="gift-amount"
                                            :style="
                                                getGiftAmountStyle(
                                                    scope.row.deductible_amount
                                                )
                                            "
                                            >¥{{
                                                scope.row.deductible_amount ||
                                                    "0.00"
                                            }}</span
                                        >
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" min-width="200">
                        <template slot-scope="scope">
                            <div class="goods-name-container">
                                <span>{{ scope.row.goods_name }}</span>
                                <el-tag
                                    size="mini"
                                    :type="
                                        getGoodsTypeTagType(
                                            scope.row.goods_type
                                        )
                                    "
                                    style="margin-left: 8px;"
                                >
                                    {{ getGoodsTypeName(scope.row.goods_type) }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="时间信息" width="200">
                        <template slot-scope="scope">
                            <div class="time-info">
                                <div class="time-item">
                                    <span class="time-label">下单:</span>
                                    <span class="time-value">{{
                                        scope.row.created_time
                                    }}</span>
                                </div>
                                <div
                                    class="time-item"
                                    v-if="scope.row.payment_time"
                                >
                                    <span class="time-label">支付:</span>
                                    <span class="time-value">{{
                                        scope.row.payment_time
                                    }}</span>
                                </div>
                                <div class="time-item" v-else>
                                    <span class="time-label">支付:</span>
                                    <span class="time-value unpaid"
                                        >未支付</span
                                    >
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="收件人信息" min-width="240">
                        <template slot-scope="scope">
                            <div class="receiver-info">
                                <div>{{ scope.row.consignee_name_mask }}</div>
                                <div>{{ scope.row.consignee_phone_mask }}</div>
                                <div class="address-text">
                                    {{ scope.row.province }}
                                    {{ scope.row.city }}
                                    {{ scope.row.district }}
                                    {{ scope.row.address }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" width="130" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.erp_status == 1"
                                size="mini"
                                type="warning"
                                @click="retryErpPush(scope.row)"
                                >重新推送</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>
    </div>
</template>

<script>
export default {
    name: "OrderManage",
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            paymentDateRange: [],
            searchForm: {
                page: 1,
                limit: 10,
                sub_order_no: "",
                goods_name: "",
                consignee_name: "",
                consignee_phone: "",
                sub_order_status: "",
                push_t_status: "",
                goods_type: "",
                box_id: "",
                payment_time_start: "",
                payment_time_end: ""
            },

            // 盲盒选项数据
            blindBoxOptions: [],

            // 模拟数据
            mockOrderList: [
                {
                    id: 1,
                    sub_order_no: "SUB202412260001",
                    goods_name: "精品红酒礼盒装",
                    created_at: "2024-12-26 10:30:00",
                    paid_at: "2024-12-26 10:35:00",
                    receiver_name: "张*三",
                    receiver_phone: "138****8888",
                    receiver_address: "北京市朝阳区某某街道123号",
                    erp_status: 2, // 推送成功
                    payment_amount: "299.00",
                    gift_amount: "20.00",
                    status: 1, // 待发货
                    goods_type: 1, // 普通商品
                    blind_box_id: null,
                    remark: "请尽快发货",
                    goods_list: [
                        {
                            id: 1,
                            name: "精品红酒礼盒装",
                            price: "199.00",
                            quantity: 1,
                            subtotal: "199.00",
                            goods_type: 1,
                            image:
                                "https://via.placeholder.com/100x100?text=红酒"
                        }
                    ]
                },
                {
                    id: 2,
                    sub_order_no: "SUB202412260002",
                    goods_name: "新年限定盲盒",
                    created_at: "2024-12-26 09:15:00",
                    paid_at: "2024-12-26 09:20:00",
                    receiver_name: "李*四",
                    receiver_phone: "139****9999",
                    receiver_address: "上海市浦东新区某某路456号",
                    erp_status: 2, // 推送成功
                    payment_amount: "588.00",
                    gift_amount: "0.00",
                    status: 2, // 已发货
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1001,
                    shipped_at: "2024-12-26 14:30:00",
                    remark: "",
                    goods_list: [
                        {
                            id: 3,
                            name: "新年限定盲盒",
                            price: "588.00",
                            quantity: 1,
                            subtotal: "588.00",
                            goods_type: 2,
                            blind_box_id: 1001,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 3,
                    sub_order_no: "SUB202412260003",
                    goods_name: "春季惊喜盲盒",
                    created_at: "2024-12-25 16:20:00",
                    paid_at: "2024-12-25 16:25:00",
                    receiver_name: "王*五",
                    receiver_phone: "137****7777",
                    receiver_address: "广州市天河区某某大道789号",
                    erp_status: 2, // 推送成功
                    payment_amount: "1299.00",
                    gift_amount: "50.00",
                    status: 3, // 已支付
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1002,
                    shipped_at: "2024-12-25 18:00:00",
                    completed_at: "2024-12-26 10:00:00",
                    remark: "感谢购买",
                    goods_list: [
                        {
                            id: 4,
                            name: "春季惊喜盲盒",
                            price: "1299.00",
                            quantity: 1,
                            subtotal: "1299.00",
                            goods_type: 2,
                            blind_box_id: 1002,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 4,
                    sub_order_no: "SUB202412260004",
                    goods_name: "精装白酒",
                    created_at: "2024-12-26 11:45:00",
                    paid_at: null,
                    receiver_name: "赵*六",
                    receiver_phone: "136****6666",
                    receiver_address: "深圳市南山区某某科技园101号",
                    erp_status: 0, // 待推送
                    payment_amount: "199.00",
                    gift_amount: "0.00",
                    status: 0, // 待支付
                    goods_type: 1, // 普通商品
                    blind_box_id: null,
                    remark: "",
                    goods_list: [
                        {
                            id: 6,
                            name: "精装白酒",
                            price: "199.00",
                            quantity: 1,
                            subtotal: "199.00",
                            goods_type: 1,
                            image:
                                "https://via.placeholder.com/100x100?text=白酒"
                        }
                    ]
                },
                {
                    id: 5,
                    sub_order_no: "SUB202412260005",
                    goods_name: "夏日清凉盲盒",
                    created_at: "2024-12-26 08:30:00",
                    paid_at: "2024-12-26 08:35:00",
                    receiver_name: "孙*七",
                    receiver_phone: "135****5555",
                    receiver_address: "杭州市西湖区某某路202号",
                    erp_status: 1, // 推送失败
                    payment_amount: "399.00",
                    gift_amount: "10.00",
                    status: 1, // 待发货
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1004,
                    remark: "ERP推送失败，需要重新推送",
                    goods_list: [
                        {
                            id: 7,
                            name: "夏日清凉盲盒",
                            price: "399.00",
                            quantity: 1,
                            subtotal: "399.00",
                            goods_type: 2,
                            blind_box_id: 1004,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 6,
                    sub_order_no: "SUB202412260006",
                    goods_name: "周年庆典盲盒",
                    created_at: "2024-12-25 20:15:00",
                    paid_at: "2024-12-25 20:20:00",
                    receiver_name: "周*八",
                    receiver_phone: "134****4444",
                    receiver_address: "成都市锦江区某某街道999号",
                    erp_status: 2, // 推送成功
                    payment_amount: "699.00",
                    gift_amount: "50.00",
                    status: 3, // 已完成
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1005,
                    shipped_at: "2024-12-26 09:00:00",
                    completed_at: "2024-12-26 15:30:00",
                    remark: "非常满意",
                    goods_list: [
                        {
                            id: 8,
                            name: "周年庆典盲盒",
                            price: "699.00",
                            quantity: 1,
                            subtotal: "699.00",
                            goods_type: 2,
                            blind_box_id: 1005,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 7,
                    sub_order_no: "SUB202412260007",
                    goods_name: "新年限定盲盒",
                    created_at: "2024-12-25 18:45:00",
                    paid_at: "2024-12-25 18:50:00",
                    receiver_name: "吴*九",
                    receiver_phone: "133****3333",
                    receiver_address: "武汉市武昌区某某路666号",
                    erp_status: 0, // 待推送
                    payment_amount: "588.00",
                    gift_amount: "0.00",
                    status: 1, // 待发货
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1001,
                    remark: "请尽快处理",
                    goods_list: [
                        {
                            id: 9,
                            name: "新年限定盲盒",
                            price: "588.00",
                            quantity: 1,
                            subtotal: "588.00",
                            goods_type: 2,
                            blind_box_id: 1001,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 8,
                    sub_order_no: "SUB202412260008",
                    goods_name: "会员专享盲盒",
                    created_at: "2024-12-25 16:30:00",
                    paid_at: "2024-12-25 16:35:00",
                    receiver_name: "郑*十",
                    receiver_phone: "132****2222",
                    receiver_address: "西安市雁塔区某某大街777号",
                    erp_status: 1, // 推送失败
                    payment_amount: "399.00",
                    gift_amount: "20.00",
                    status: 1, // 待发货
                    goods_type: 2, // 盲盒商品
                    blind_box_id: 1003,
                    remark: "ERP推送失败，需要重新推送",
                    goods_list: [
                        {
                            id: 10,
                            name: "会员专享盲盒",
                            price: "399.00",
                            quantity: 1,
                            subtotal: "399.00",
                            goods_type: 2,
                            blind_box_id: 1003,
                            image:
                                "https://via.placeholder.com/100x100?text=盲盒"
                        }
                    ]
                },
                {
                    id: 9,
                    sub_order_no: "SUB202412260009",
                    goods_name: "高端抽奖商品",
                    created_at: "2024-12-25 14:20:00",
                    paid_at: "2024-12-25 14:25:00",
                    receiver_name: "冯*一",
                    receiver_phone: "131****1111",
                    receiver_address: "南京市建邺区某某路555号",
                    erp_status: 2, // 推送成功
                    payment_amount: "1299.00",
                    gift_amount: "100.00",
                    status: 2, // 已发货
                    goods_type: 3, // 抽奖商品
                    blind_box_id: null,
                    shipped_at: "2024-12-25 18:00:00",
                    remark: "抽奖获得的商品",
                    goods_list: [
                        {
                            id: 11,
                            name: "高端抽奖商品",
                            price: "1299.00",
                            quantity: 1,
                            subtotal: "1299.00",
                            goods_type: 3,
                            image:
                                "https://via.placeholder.com/100x100?text=抽奖"
                        }
                    ]
                }
            ]
        };
    },
    watch: {
        "searchForm.goods_type"(newVal) {
            // 当商品类型不是盲盒商品时，清空盲盒筛选
            if (newVal !== "3") {
                this.searchForm.box_id = "";
            }
            // 当选择盲盒商品时，获取盲盒列表
            if (newVal === "3") {
                this.getBlindBoxList();
            }
        },
        paymentDateRange(val) {
            if (val && val.length === 2) {
                this.searchForm.payment_time_start = val[0];
                this.searchForm.payment_time_end = val[1];
            } else {
                this.searchForm.payment_time_start = "";
                this.searchForm.payment_time_end = "";
            }
        }
    },
    mounted() {
        this.getOrderList();
        this.initBlindBoxOptions();
    },
    methods: {
        // 获取订单列表
        async getOrderList() {
            this.loading = true;

            try {
                // 构建API请求参数
                const params = {
                    page: this.searchForm.page,
                    limit: this.searchForm.limit
                };

                // 添加搜索条件
                if (this.searchForm.sub_order_no) {
                    params.sub_order_no = this.searchForm.sub_order_no;
                }

                if (this.searchForm.goods_name) {
                    params.goods_name = this.searchForm.goods_name;
                }

                if (this.searchForm.consignee_name) {
                    params.consignee_name = this.searchForm.consignee_name;
                }

                if (this.searchForm.consignee_phone) {
                    params.consignee_phone = this.searchForm.consignee_phone;
                }

                if (this.searchForm.sub_order_status) {
                    params.sub_order_status = parseInt(
                        this.searchForm.sub_order_status
                    );
                }

                if (this.searchForm.push_t_status) {
                    params.push_t_status = parseInt(
                        this.searchForm.push_t_status
                    );
                }

                if (this.searchForm.goods_type) {
                    params.goods_type = parseInt(this.searchForm.goods_type);
                }

                if (this.searchForm.box_id) {
                    params.box_id = parseInt(this.searchForm.box_id);
                }

                if (this.searchForm.payment_time_start) {
                    params.payment_time_start = this.searchForm.payment_time_start;
                }

                if (this.searchForm.payment_time_end) {
                    params.payment_time_end = this.searchForm.payment_time_end;
                }

                // 调用API
                const response = await this.$request.main.getOrderList(params);

                if (response.data.error_code === 0) {
                    const data = response.data.data;
                    this.total = data.total;
                    this.tableData = data.list || [];
                } else {
                    this.$message.error(
                        response.data.error_msg || "获取订单列表失败"
                    );
                    this.tableData = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("获取订单列表失败:", error);
                this.$message.error("获取订单列表失败，请稍后重试");
                this.tableData = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getOrderList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                sub_order_no: "",
                goods_name: "",
                consignee_name: "",
                consignee_phone: "",
                sub_order_status: "",
                push_t_status: "",
                goods_type: "",
                box_id: "",
                payment_time_start: "",
                payment_time_end: ""
            };
            this.paymentDateRange = [];
            this.getOrderList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getOrderList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getOrderList();
        },

        // 获取订单状态名称
        getStatusName(status) {
            const statusMap = {
                0: "待支付",
                1: "已支付",
                2: "已发货",
                3: "已完成",
                4: "已取消",
                5: "已退款",
                6: "退款中",
                7: "退款失败"
            };
            return statusMap[status] || "未知";
        },
        // 获取订单状态颜色
        getStatusColor(status) {
            const colorMap = {
                0: "warning",
                1: "primary",
                2: "info",
                3: "success",
                4: "danger",
                5: "success",
                6: "warning",
                7: "danger"
            };
            return colorMap[status] || "info";
        },
        // 获取ERP推送状态名称
        getErpStatusName(status) {
            const statusMap = {
                0: "未推送",
                1: "推送成功",
                2: "推送失败",
                3: "不推送"
            };
            return statusMap[status] || "未知";
        },
        // 获取ERP推送状态颜色
        getErpStatusColor(status) {
            const colorMap = {
                0: "warning",
                1: "success",
                2: "danger",
                3: "info"
            };
            return colorMap[status] || "info";
        },
        // 获取退款状态名称
        getRefundStatusName(status) {
            const statusMap = {
                0: "未退款",
                1: "退款中",
                2: "退款成功",
                3: "退款失败"
            };
            return statusMap[status] || "未知";
        },
        // 获取退款状态颜色
        getRefundStatusColor(status) {
            const colorMap = {
                0: "info",
                1: "warning",
                2: "success",
                3: "danger"
            };
            return colorMap[status] || "info";
        },

        // 获取礼金抵扣金额的样式
        getGiftAmountStyle(amount) {
            const giftAmount = parseFloat(amount) || 0;
            if (giftAmount === 0) {
                return { color: "#f56c6c", fontWeight: "bold" }; // 红色
            } else {
                return { color: "#67c23a", fontWeight: "bold" }; // 绿色
            }
        },
        // 重新推送ERP
        retryErpPush(row) {
            this.$confirm("确定要重新推送到ERP系统吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                // 模拟ERP推送
                const orderIndex = this.mockOrderList.findIndex(
                    item => item.id === row.id
                );
                if (orderIndex !== -1) {
                    // 模拟推送成功
                    this.mockOrderList[orderIndex].erp_status = 2;
                    this.$message.success("ERP推送成功");
                    this.getOrderList();
                } else {
                    this.$message.error("ERP推送失败，订单不存在");
                }
            });
        },
        // 获取盲盒列表
        async getBlindBoxList() {
            try {
                const response = await this.$request.main.getBlindBoxList({
                    page: 1,
                    limit: 100
                });

                if (response.data.error_code === 0) {
                    this.blindBoxOptions = response.data.data.list || [];
                } else {
                    console.error("获取盲盒列表失败:", response.data.error_msg);
                    this.blindBoxOptions = [];
                }
            } catch (error) {
                console.error("获取盲盒列表失败:", error);
                this.blindBoxOptions = [];
            }
        },
        // 初始化盲盒选项数据
        initBlindBoxOptions() {
            // 初始化时不加载盲盒数据，只有当选择盲盒商品类型时才加载
            this.blindBoxOptions = [];
        },
        // 获取商品类型名称
        getGoodsTypeName(type) {
            const typeMap = {
                1: "普通商品",
                2: "抽奖商品",
                3: "盲盒商品",
                4: "赠送订单"
            };
            return typeMap[type] || "未知类型";
        },
        // 获取商品类型标签颜色
        getGoodsTypeTagType(type) {
            const typeMap = {
                1: "", // 默认颜色
                2: "success", // 绿色 - 抽奖商品
                3: "warning" // 橙色 - 盲盒商品
            };
            return typeMap[type] || "info";
        }
    }
};
</script>

<style scoped>
.order-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    background: #fff;
}

.goods-item {
    margin-bottom: 5px;
    font-size: 12px;
}

.goods-item:last-child {
    margin-bottom: 0;
}

.receiver-info {
    font-size: 12px;
    line-height: 1.4;
}

.receiver-info > div {
    margin-bottom: 2px;
}

.receiver-info > div:last-child {
    margin-bottom: 0;
}

.goods-name-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.goods-name-container span {
    margin-right: 4px;
}

.time-info {
    font-size: 12px;
    line-height: 1.4;
}

.time-item {
    margin-bottom: 3px;
    display: flex;
    align-items: center;
}

.time-item:last-child {
    margin-bottom: 0;
}

.time-label {
    color: #909399;
    font-weight: 500;
    min-width: 32px;
    margin-right: 4px;
}

.time-value {
    color: #303133;
    flex: 1;
}

.time-value.unpaid {
    color: #f56c6c;
    font-weight: 500;
}

.order-info {
    font-size: 12px;
    line-height: 1.4;
}

.order-no {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
}

.status-tags {
    margin-top: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.payment-info {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
}

.payment-item-inline {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.payment-label {
    color: #909399;
    font-weight: 500;
    margin-right: 4px;
}

.payment-amount {
    color: #f56c6c;
    font-weight: bold;
    margin-right: 8px;
}

.gift-amount {
    font-weight: bold;
}

.address-text {
    color: #666;
    max-width: 180px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 20px;
}
</style>
