<template>
    <div class="blind-box-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="盲盒名称">
                        <el-input
                            v-model="searchForm.name"
                            placeholder="盲盒名称"
                            clearable
                            style="width: 200px;"
                        />
                    </el-form-item>
                    <el-form-item label="盲盒ID">
                        <el-input
                            v-model="searchForm.id"
                            placeholder="盲盒ID"
                            clearable
                            style="width: 120px;"
                        />
                    </el-form-item>
                    <el-form-item label="周期单位">
                        <el-select
                            v-model="searchForm.cycle_unit"
                            placeholder="周期单位"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="小时" value="hour"></el-option>
                            <el-option label="天" value="day"></el-option>
                            <el-option label="周" value="week"></el-option>
                            <el-option label="月" value="month"></el-option>
                            <el-option label="年" value="year"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="状态"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="上架" value="active"></el-option>
                            <el-option
                                label="下架"
                                value="inactive"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间" style="min-width: 380px;">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 100%;"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="success" @click="showAddDialog"
                            >新增盲盒</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column
                        prop="id"
                        label="盲盒ID"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        prop="name"
                        label="盲盒名称"
                        width="200"
                    ></el-table-column>
                    <el-table-column
                        prop="cycle"
                        label="周期"
                        width="80"
                    ></el-table-column>
                    <el-table-column
                        prop="cycle_unit"
                        label="周期单位"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <el-tag
                                size="mini"
                                :type="
                                    getCycleUnitTagType(scope.row.cycle_unit)
                                "
                            >
                                {{ getCycleUnitText(scope.row.cycle_unit) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="created_at"
                        label="创建时间"
                        width="180"
                    ></el-table-column>
                    <el-table-column prop="activated_count" width="100">
                        <template slot="header">
                            <span>激活人数</span>
                            <el-tooltip
                                content="代表礼品卡被成功激活次数（不对用户去重）"
                                placement="top"
                                effect="dark"
                            >
                                <i
                                    class="el-icon-question help-icon"
                                    aria-label="激活人数说明"
                                ></i>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="claimed_count" width="100">
                        <template slot="header">
                            <span>领取人数</span>
                            <el-tooltip
                                content="礼品卡商品被领取次数（去重后的数据，领取多次也只算一次）"
                                placement="top"
                                effect="dark"
                            >
                                <i
                                    class="el-icon-question help-icon"
                                    aria-label="领取人数说明"
                                ></i>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag
                                size="mini"
                                :type="
                                    scope.row.status === 'active'
                                        ? 'success'
                                        : 'danger'
                                "
                            >
                                {{
                                    scope.row.status === "active"
                                        ? "上架"
                                        : "下架"
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="350">
                        <template slot-scope="scope">
                            <el-button
                                size="mini"
                                type="primary"
                                @click="editBlindBox(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                size="mini"
                                type="success"
                                @click="bindProducts(scope.row)"
                                >绑定商品</el-button
                            >
                            <el-button
                                size="mini"
                                type="info"
                                @click="viewBlindBoxDetail(scope.row)"
                                >盲盒详情</el-button
                            >
                            <el-button
                                size="mini"
                                :type="
                                    scope.row.status === 'active'
                                        ? 'warning'
                                        : 'success'
                                "
                                @click="toggleBlindBoxStatus(scope.row)"
                                >{{
                                    scope.row.status === "active"
                                        ? "下架"
                                        : "上架"
                                }}</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="searchForm.page"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="searchForm.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                    >
                    </el-pagination>
                </div>
            </el-card>
        </div>

        <!-- 新增/编辑盲盒对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form
                :model="blindBoxForm"
                :rules="dynamicBlindBoxRules"
                ref="blindBoxForm"
                label-width="100px"
            >
                <el-form-item label="盲盒名称" prop="title">
                    <el-input
                        v-model="blindBoxForm.title"
                        placeholder="请输入盲盒名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="封面图" prop="avatar_image">
                    <vos-oss
                        :key="
                            `blindbox-image-${dialogTitle}-${blindBoxForm.id ||
                                'new'}`
                        "
                        list-type="picture-card"
                        :showFileList="true"
                        :dir="dir"
                        :file-list="blindBoxForm.avatar_image"
                        :limit="1"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                    <div class="upload-tip">
                        建议尺寸：300x300px，支持JPG、PNG格式，大小不超过2MB
                    </div>
                </el-form-item>
                <!-- 新增时显示的字段 -->
                <template v-if="!isEdit">
                    <el-form-item label="周期数量" prop="valid_time_num">
                        <el-input-number
                            v-model="blindBoxForm.valid_time_num"
                            :min="1"
                            :max="100"
                            placeholder="请输入周期数量"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="周期单位" prop="valid_time_unit">
                        <el-select
                            v-model="blindBoxForm.valid_time_unit"
                            placeholder="请选择周期单位"
                            style="width: 100%;"
                        >
                            <el-option label="小时" value="hour"></el-option>
                            <el-option label="天" value="day"></el-option>
                            <el-option label="周" value="week"></el-option>
                            <el-option label="月" value="month"></el-option>
                            <el-option label="年" value="year"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="售价" prop="price">
                        <el-input-number
                            v-model="blindBoxForm.price"
                            :min="0"
                            :precision="2"
                            placeholder="请输入售价"
                            style="width: 100%;"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="上架状态" prop="onsale_status">
                        <el-radio-group v-model="blindBoxForm.onsale_status">
                            <el-radio :label="2">在售中</el-radio>
                            <el-radio :label="3">已下架</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </template>

                <!-- 编辑时显示的只读信息 -->
                <template v-else>
                    <el-form-item label="周期">
                        <el-input
                            :value="
                                blindBoxForm.valid_time_num +
                                    ' ' +
                                    getCycleUnitText(
                                        blindBoxForm.valid_time_unit
                                    )
                            "
                            readonly
                            placeholder="周期不可修改"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="售价">
                        <el-input
                            :value="'¥' + blindBoxForm.price"
                            readonly
                            placeholder="售价不可修改"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="上架状态">
                        <el-input
                            :value="
                                blindBoxForm.onsale_status === 2
                                    ? '在售中'
                                    : '已下架'
                            "
                            readonly
                            placeholder="上架状态不可修改"
                        ></el-input>
                        <div class="form-tip">
                            注意：周期、售价、上架状态等字段不允许修改，避免影响已生成的礼品卡
                        </div>
                    </el-form-item>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitBlindBox"
                    >确 定</el-button
                >
            </div>
        </el-dialog>

        <!-- 盲盒详情对话框 -->
        <el-dialog
            title="盲盒详情"
            :visible.sync="detailDialogVisible"
            width="90%"
            :close-on-click-modal="false"
            custom-class="blind-box-detail-dialog"
        >
            <div class="blind-box-detail">
                <!-- 盲盒概览卡片 -->
                <div class="detail-overview">
                    <el-row :gutter="24">
                        <!-- 左侧：封面图片 -->
                        <el-col :span="6">
                            <div class="cover-section">
                                <div class="cover-image-container">
                                    <el-image
                                        :src="
                                            currentBlindBox.cover_image ||
                                                currentBlindBox.avatar_image
                                        "
                                        fit="cover"
                                        class="cover-image"
                                    >
                                        <div slot="error" class="image-slot">
                                            <i
                                                class="el-icon-picture-outline"
                                            ></i>
                                            <p>暂无封面</p>
                                        </div>
                                    </el-image>
                                </div>
                                <div class="status-badge">
                                    <el-tag
                                        :type="
                                            currentBlindBox.status === 'active'
                                                ? 'success'
                                                : 'danger'
                                        "
                                        size="medium"
                                    >
                                        {{
                                            currentBlindBox.status === "active"
                                                ? "上架中"
                                                : "已下架"
                                        }}
                                    </el-tag>
                                </div>
                            </div>
                        </el-col>

                        <!-- 右侧：基本信息 -->
                        <el-col :span="18">
                            <div class="info-section">
                                <div class="title-section">
                                    <h2 class="blind-box-title">
                                        {{
                                            currentBlindBox.title ||
                                                currentBlindBox.name
                                        }}
                                    </h2>
                                    <div class="title-meta">
                                        <span class="blind-box-id"
                                            >ID: {{ currentBlindBox.id }}</span
                                        >
                                        <span class="price-tag"
                                            >¥{{
                                                currentBlindBox.price || 0
                                            }}</span
                                        >
                                    </div>
                                </div>

                                <!-- 统计数据卡片 -->
                                <div class="stats-cards">
                                    <el-row :gutter="16">
                                        <el-col :span="8">
                                            <div class="stat-card">
                                                <div class="stat-icon active">
                                                    <i
                                                        class="el-icon-check"
                                                    ></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number">
                                                        {{
                                                            currentBlindBox.active_num ||
                                                                currentBlindBox.activated_count ||
                                                                0
                                                        }}
                                                    </div>
                                                    <div class="stat-label">
                                                        激活数量
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="stat-card">
                                                <div class="stat-icon claimed">
                                                    <i
                                                        class="el-icon-present"
                                                    ></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number">
                                                        {{
                                                            currentBlindBox.get_num ||
                                                                currentBlindBox.claimed_count ||
                                                                0
                                                        }}
                                                    </div>
                                                    <div class="stat-label">
                                                        领取数量
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="stat-card">
                                                <div class="stat-icon cycle">
                                                    <i class="el-icon-time"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number">
                                                        {{
                                                            currentBlindBox.valid_time_num ||
                                                                currentBlindBox.cycle
                                                        }}
                                                        {{
                                                            getCycleUnitText(
                                                                currentBlindBox.valid_time_unit ||
                                                                    currentBlindBox.cycle_unit
                                                            )
                                                        }}
                                                    </div>
                                                    <div class="stat-label">
                                                        周期设置
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>

                                <!-- 时间信息 -->
                                <div class="time-info">
                                    <el-row :gutter="24">
                                        <el-col :span="12">
                                            <div class="time-item">
                                                <i class="el-icon-date"></i>
                                                <span class="time-label"
                                                    >创建时间：</span
                                                >
                                                <span class="time-value">{{
                                                    currentBlindBox.created_time ||
                                                        currentBlindBox.created_at
                                                }}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="12">
                                            <div class="time-item">
                                                <i class="el-icon-edit"></i>
                                                <span class="time-label"
                                                    >更新时间：</span
                                                >
                                                <span class="time-value">{{
                                                    currentBlindBox.update_time ||
                                                        "-"
                                                }}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <!-- 商品配置信息 -->
                <div class="detail-content">
                    <div class="section-header">
                        <h3>
                            <i class="el-icon-goods"></i>
                            商品配置信息
                        </h3>
                        <div class="section-meta">
                            共 {{ cycleDetailData.length }} 个配置
                        </div>
                    </div>

                    <div class="product-configs">
                        <div
                            v-for="(config, index) in cycleDetailData"
                            :key="index"
                            class="config-card"
                        >
                            <div class="config-header">
                                <div class="config-title">
                                    <span class="config-number"
                                        >配置 {{ config.cycle_number }}</span
                                    >
                                    <el-tag type="success" size="small">
                                        已领取
                                        {{ config.claimed_count || 0 }} 次
                                    </el-tag>
                                </div>
                            </div>

                            <div class="config-products">
                                <div
                                    v-for="product in config.products"
                                    :key="product.id"
                                    class="product-card"
                                >
                                    <div class="product-details">
                                        <div class="product-name">
                                            {{ product.name }}
                                        </div>
                                        <div class="product-meta">
                                            <span class="product-code">{{
                                                product.short_code
                                            }}</span>
                                            <span class="product-quantity"
                                                >× {{ product.quantity }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="detailDialogVisible = false" size="medium">
                    关 闭
                </el-button>
            </div>
        </el-dialog>

        <!-- 绑定商品对话框 -->
        <el-dialog
            title="绑定商品"
            :visible.sync="bindProductDialogVisible"
            width="80%"
            :close-on-click-modal="false"
        >
            <div class="bind-product-content">
                <div class="bind-header">
                    <h3>
                        {{
                            currentBindBlindBox.name ||
                                currentBindBlindBox.title
                        }}
                    </h3>
                    <p>
                        为盲盒配置商品信息（共{{
                            currentBindBlindBox.cycle ||
                                currentBindBlindBox.valid_time_num
                        }}个周期配置）
                    </p>
                </div>

                <el-form
                    :model="bindProductForm"
                    ref="bindProductForm"
                    label-width="120px"
                >
                    <div
                        v-for="(cycle, index) in bindProductForm.cycles"
                        :key="index"
                        class="cycle-bind-section"
                    >
                        <div class="cycle-header">
                            <h4>配置 {{ cycle.cycle_number }}</h4>
                        </div>

                        <el-form-item label="发货类型">
                            <el-select
                                v-model="cycle.product_type"
                                placeholder="请选择发货类型"
                                style="width: 100%;"
                            >
                                <el-option
                                    label="随机"
                                    value="random"
                                ></el-option>
                                <el-option
                                    label="固定"
                                    value="fixed"
                                ></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item
                            :label="`配置${cycle.cycle_number}商品`"
                            :prop="`cycles.${index}.product_input`"
                        >
                            <el-input
                                v-model="cycle.product_input"
                                placeholder="请输入商品简码，格式：简码*数量+简码*数量（如：WINE001*2+TEA001*1）"
                                @keyup.enter="addProductsFromInput(index)"
                                style="width: 100%;"
                            >
                                <el-button
                                    slot="append"
                                    @click="addProductsFromInput(index)"
                                    type="primary"
                                >
                                    添加
                                </el-button>
                            </el-input>
                            <div class="input-tip">
                                支持格式：简码*数量+简码*数量，如：WINE001*2+TEA001*1
                            </div>
                        </el-form-item>

                        <!-- 显示已选择的商品 -->
                        <div
                            v-if="cycle.products && cycle.products.length > 0"
                            class="selected-products"
                        >
                            <div class="selected-products-title">
                                已选择商品：
                            </div>
                            <div class="product-list-container">
                                <div
                                    v-for="(product,
                                    productIndex) in cycle.products"
                                    :key="productIndex"
                                    class="selected-product-item"
                                >
                                    <div class="product-info-section">
                                        <div class="product-name">
                                            {{
                                                product.name ||
                                                    product.short_code
                                            }}
                                        </div>
                                        <div class="product-code-quantity">
                                            <span class="product-code"
                                                >简码:
                                                {{ product.short_code }}</span
                                            >
                                            <span class="quantity-label"
                                                >数量:</span
                                            >
                                            <el-input-number
                                                v-model="product.quantity"
                                                :min="1"
                                                :max="999"
                                                size="mini"
                                                style="width: 80px;"
                                            ></el-input-number>
                                            <span class="quantity-unit"
                                                >件</span
                                            >
                                        </div>
                                    </div>
                                    <div class="product-actions">
                                        <el-button
                                            size="mini"
                                            type="danger"
                                            icon="el-icon-delete"
                                            @click="
                                                removeProduct(
                                                    index,
                                                    productIndex
                                                )
                                            "
                                            circle
                                        ></el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="bindProductDialogVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="submitBindProducts"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";

export default {
    name: "BlindBoxManage",
    components: {
        vosOss
    },
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            dateRange: [],
            dir: "vinehoo/mulando-hotel/blindbox/",
            searchForm: {
                page: 1,
                limit: 10,
                name: "",
                id: "",
                cycle_unit: "",
                status: "",
                start_date: "",
                end_date: ""
            },
            dialogVisible: false,
            detailDialogVisible: false,
            bindProductDialogVisible: false,
            dialogTitle: "新增盲盒",
            isEdit: false,
            currentBlindBox: {},
            currentBindBlindBox: {},
            cycleDetailData: [],
            bindProductForm: {
                cycles: []
            },
            blindBoxForm: {
                id: "",
                title: "",
                avatar_image: [],
                valid_time_num: 1,
                valid_time_unit: "week",
                price: 0,
                onsale_status: 3
            },
            blindBoxRules: {
                title: [
                    {
                        required: true,
                        message: "请输入盲盒名称",
                        trigger: "blur"
                    }
                ],
                avatar_image: [
                    {
                        required: true,
                        message: "请上传封面图",
                        trigger: "change"
                    }
                ],
                valid_time_num: [
                    {
                        required: true,
                        message: "请输入周期数量",
                        trigger: "blur"
                    }
                ],
                valid_time_unit: [
                    {
                        required: true,
                        message: "请选择周期单位",
                        trigger: "change"
                    }
                ]
            },
            // 模拟盲盒数据
            mockBlindBoxList: [
                {
                    id: 1001,
                    name: "新年限定盲盒",
                    cover_image:
                        "https://via.placeholder.com/300x300/ff6b6b/ffffff?text=新年",
                    cycle: 2,
                    cycle_unit: "week",
                    status: "active",
                    created_at: "2024-01-15 10:30:00",
                    activated_count: 156,
                    claimed_count: 89
                },
                {
                    id: 1002,
                    name: "春季惊喜盲盒",
                    cover_image:
                        "https://via.placeholder.com/300x300/4ecdc4/ffffff?text=春季",
                    cycle: 1,
                    cycle_unit: "month",
                    status: "active",
                    created_at: "2024-02-01 14:20:00",
                    activated_count: 234,
                    claimed_count: 178
                },
                {
                    id: 1003,
                    name: "会员专享盲盒",
                    cover_image:
                        "https://via.placeholder.com/300x300/45b7d1/ffffff?text=会员",
                    cycle: 3,
                    cycle_unit: "week",
                    status: "inactive",
                    created_at: "2024-02-10 09:15:00",
                    activated_count: 98,
                    claimed_count: 67
                },
                {
                    id: 1004,
                    name: "夏日清凉盲盒",
                    cover_image:
                        "https://via.placeholder.com/300x300/96ceb4/ffffff?text=夏日",
                    cycle: 2,
                    cycle_unit: "month",
                    status: "active",
                    created_at: "2024-03-01 16:45:00",
                    activated_count: 312,
                    claimed_count: 245
                },
                {
                    id: 1005,
                    name: "周年庆典盲盒",
                    cover_image:
                        "https://via.placeholder.com/300x300/feca57/ffffff?text=庆典",
                    cycle: 1,
                    cycle_unit: "week",
                    status: "inactive",
                    created_at: "2024-03-15 11:30:00",
                    activated_count: 445,
                    claimed_count: 389
                }
            ],
            // 模拟周期详情数据
            mockCycleDetails: {
                1001: [
                    {
                        cycle_number: 1,
                        start_time: "2024-01-15 00:00:00",
                        end_time: "2024-01-28 23:59:59",
                        claimed_count: 45,
                        products: [
                            {
                                id: 101,
                                name: "精品红酒礼盒",
                                short_code: "WINE001",
                                image:
                                    "https://via.placeholder.com/60x60/ff6b6b/ffffff?text=酒",
                                quantity: 2
                            },
                            {
                                id: 102,
                                name: "高档茶叶套装",
                                short_code: "TEA001",
                                image:
                                    "https://via.placeholder.com/60x60/4ecdc4/ffffff?text=茶",
                                quantity: 1
                            }
                        ]
                    },
                    {
                        cycle_number: 2,
                        start_time: "2024-01-29 00:00:00",
                        end_time: "2024-02-11 23:59:59",
                        claimed_count: 32,
                        products: [
                            {
                                id: 103,
                                name: "进口巧克力",
                                short_code: "CHOC001",
                                image:
                                    "https://via.placeholder.com/60x60/45b7d1/ffffff?text=巧",
                                quantity: 3
                            }
                        ]
                    }
                ],
                1002: [
                    {
                        cycle_number: 1,
                        start_time: "2024-02-01 00:00:00",
                        end_time: "2024-02-29 23:59:59",
                        claimed_count: 67,
                        products: [
                            {
                                id: 104,
                                name: "春季护肤套装",
                                short_code: "SKIN001",
                                image:
                                    "https://via.placeholder.com/60x60/96ceb4/ffffff?text=护",
                                quantity: 1
                            },
                            {
                                id: 105,
                                name: "花草茶礼盒",
                                short_code: "HERB001",
                                image:
                                    "https://via.placeholder.com/60x60/feca57/ffffff?text=花",
                                quantity: 2
                            }
                        ]
                    }
                ]
            },
            // 可用商品列表
            availableProducts: [
                {
                    id: 101,
                    name: "精品红酒礼盒",
                    short_code: "WINE001",
                    image:
                        "https://via.placeholder.com/60x60/ff6b6b/ffffff?text=酒"
                },
                {
                    id: 102,
                    name: "高档茶叶套装",
                    short_code: "TEA001",
                    image:
                        "https://via.placeholder.com/60x60/4ecdc4/ffffff?text=茶"
                },
                {
                    id: 103,
                    name: "进口巧克力",
                    short_code: "CHOC001",
                    image:
                        "https://via.placeholder.com/60x60/45b7d1/ffffff?text=巧"
                },
                {
                    id: 104,
                    name: "春季护肤套装",
                    short_code: "SKIN001",
                    image:
                        "https://via.placeholder.com/60x60/96ceb4/ffffff?text=护"
                },
                {
                    id: 105,
                    name: "花草茶礼盒",
                    short_code: "HERB001",
                    image:
                        "https://via.placeholder.com/60x60/feca57/ffffff?text=花"
                },
                {
                    id: 106,
                    name: "咖啡豆礼盒",
                    short_code: "COFFEE001",
                    image:
                        "https://via.placeholder.com/60x60/8b5a3c/ffffff?text=咖"
                },
                {
                    id: 107,
                    name: "蜂蜜套装",
                    short_code: "HONEY001",
                    image:
                        "https://via.placeholder.com/60x60/f39c12/ffffff?text=蜜"
                },
                {
                    id: 108,
                    name: "坚果礼盒",
                    short_code: "NUTS001",
                    image:
                        "https://via.placeholder.com/60x60/d35400/ffffff?text=果"
                }
            ]
        };
    },
    computed: {
        // 动态验证规则，编辑时只验证可编辑字段
        dynamicBlindBoxRules() {
            const baseRules = {
                title: [
                    {
                        required: true,
                        message: "请输入盲盒名称",
                        trigger: "blur"
                    }
                ],
                avatar_image: [
                    {
                        required: true,
                        message: "请上传封面图",
                        trigger: "change"
                    }
                ]
            };

            // 新增时添加其他字段的验证
            if (!this.isEdit) {
                baseRules.valid_time_num = [
                    {
                        required: true,
                        message: "请输入周期数量",
                        trigger: "blur"
                    }
                ];
                baseRules.valid_time_unit = [
                    {
                        required: true,
                        message: "请选择周期单位",
                        trigger: "change"
                    }
                ];
            }

            return baseRules;
        }
    },
    mounted() {
        this.getBlindBoxList();
    },
    methods: {
        // 获取盲盒列表
        async getBlindBoxList() {
            this.loading = true;

            try {
                // 构建API请求参数
                const params = {
                    page: this.searchForm.page,
                    limit: this.searchForm.limit
                };

                // 添加搜索条件
                if (this.searchForm.name) {
                    params.title = this.searchForm.name;
                }

                if (this.searchForm.id) {
                    params.id = parseInt(this.searchForm.id);
                }

                if (this.searchForm.cycle_unit) {
                    params.valid_time_unit = this.searchForm.cycle_unit;
                }

                if (this.searchForm.status) {
                    // 将前端状态映射为API状态
                    params.onsale_status =
                        this.searchForm.status === "active" ? 2 : 3;
                }

                if (this.searchForm.start_date) {
                    params.start_date = this.searchForm.start_date;
                }

                if (this.searchForm.end_date) {
                    params.end_date = this.searchForm.end_date;
                }

                // 调用API
                const response = await this.$request.main.getBlindBoxList(
                    params
                );

                if (response.data.error_code === 0) {
                    const data = response.data.data;
                    this.total = data.total;

                    // 转换API数据格式为前端需要的格式
                    this.tableData = data.list.map(item => ({
                        id: item.id,
                        name: item.title,
                        cover_image: item.avatar_image,
                        cycle: item.valid_time_num,
                        cycle_unit: item.valid_time_unit,
                        status:
                            item.onsale_status === 2 ? "active" : "inactive",
                        created_at: item.created_time,
                        activated_count: item.active_num,
                        claimed_count: item.get_num,
                        price: item.price,
                        create_num: item.create_num
                    }));
                } else {
                    this.$message.error(
                        response.data.error_msg || "获取盲盒列表失败"
                    );
                    this.tableData = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("获取盲盒列表失败:", error);
                this.$message.error("获取盲盒列表失败，请稍后重试");
                this.tableData = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getBlindBoxList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                name: "",
                id: "",
                cycle_unit: "",
                status: "",
                start_date: "",
                end_date: ""
            };
            this.dateRange = [];
            this.getBlindBoxList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getBlindBoxList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getBlindBoxList();
        },
        // 监听日期范围变化
        handleDateRangeChange() {
            if (this.dateRange && this.dateRange.length === 2) {
                this.searchForm.start_date = this.dateRange[0];
                this.searchForm.end_date = this.dateRange[1];
            } else {
                this.searchForm.start_date = "";
                this.searchForm.end_date = "";
            }
        },
        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = "新增盲盒";
            this.isEdit = false;
            this.blindBoxForm = {
                id: "",
                title: "",
                avatar_image: [],
                valid_time_num: 1,
                valid_time_unit: "week",
                price: 0,
                onsale_status: 3
            };
            // 确保表单验证状态也被重置
            this.$nextTick(() => {
                if (this.$refs.blindBoxForm) {
                    this.$refs.blindBoxForm.clearValidate();
                }
            });
            this.dialogVisible = true;
        },
        // 编辑盲盒
        editBlindBox(row) {
            this.dialogTitle = "编辑盲盒";
            this.isEdit = true;
            // 转换数据格式以适配表单
            this.blindBoxForm = {
                id: row.id,
                title: row.name || row.title,
                avatar_image: row.cover_image
                    ? [row.cover_image]
                    : row.avatar_image || [],
                valid_time_num: row.cycle || row.valid_time_num,
                valid_time_unit: row.cycle_unit || row.valid_time_unit,
                price: row.price || 0,
                onsale_status: row.status === "active" ? 2 : 3
            };
            this.dialogVisible = true;
        },
        // 提交盲盒表单
        async submitBlindBox() {
            this.$refs.blindBoxForm.validate(async valid => {
                if (valid) {
                    try {
                        // 准备API数据
                        const apiData = {
                            title: this.blindBoxForm.title,
                            avatar_image:
                                this.blindBoxForm.avatar_image.length > 0
                                    ? this.blindBoxForm.avatar_image[0]
                                    : "",
                            valid_time_unit: this.blindBoxForm.valid_time_unit,
                            valid_time_num: this.blindBoxForm.valid_time_num,
                            price: this.blindBoxForm.price,
                            onsale_status: this.blindBoxForm.onsale_status
                        };

                        if (this.isEdit) {
                            // 编辑逻辑 - 调用API，只传递可编辑的字段
                            const editData = {
                                id: this.blindBoxForm.id,
                                title: this.blindBoxForm.title,
                                avatar_image:
                                    this.blindBoxForm.avatar_image.length > 0
                                        ? this.blindBoxForm.avatar_image[0]
                                        : ""
                            };

                            const response = await this.$request.main.updateBlindBox(
                                editData
                            );

                            if (response.data.error_code === 0) {
                                this.$message.success("编辑成功");
                                this.dialogVisible = false;
                                this.getBlindBoxList();
                            }
                        } else {
                            // 新增逻辑 - 调用API
                            const response = await this.$request.main.createBlindBox(
                                apiData
                            );

                            if (response.data.error_code === 0) {
                                this.$message.success("新增成功");
                                this.dialogVisible = false;
                                this.getBlindBoxList();
                            }
                        }
                    } catch (error) {
                        console.error("提交盲盒失败:", error);
                        this.$message.error("操作失败，请重试");
                    }
                } else {
                    return false;
                }
            });
        },
        // 查看盲盒详情
        async viewBlindBoxDetail(row) {
            this.detailDialogVisible = true;

            try {
                // 调用盲盒详情API，不使用列表数据
                const response = await this.$request.main.getBlindBoxDetail({
                    id: row.id
                });

                if (response.data.error_code === 0) {
                    const detailData = response.data.data;

                    // 使用接口返回的完整数据更新当前盲盒信息
                    this.currentBlindBox = {
                        id: detailData.id,
                        name: detailData.title,
                        title: detailData.title,
                        avatar_image: detailData.avatar_image,
                        cover_image: detailData.avatar_image,
                        valid_time_unit: detailData.valid_time_unit,
                        valid_time_num: detailData.valid_time_num,
                        cycle: detailData.valid_time_num,
                        cycle_unit: detailData.valid_time_unit,
                        price: detailData.price,
                        status:
                            detailData.onsale_status === 2
                                ? "active"
                                : "inactive",
                        create_num: detailData.create_num,
                        active_num: detailData.active_num,
                        activated_count: detailData.active_num,
                        get_num: detailData.get_num,
                        claimed_count: detailData.get_num,
                        created_time: detailData.created_time,
                        created_at: detailData.created_time,
                        update_time: detailData.update_time
                    };

                    // 处理商品配置数据，转换为前端需要的格式
                    if (detailData.items && detailData.items.length > 0) {
                        this.cycleDetailData = detailData.items.map(
                            (item, index) => ({
                                cycle_number: index + 1, // 使用索引+1作为周期序号
                                item_id: item.id, // 保存商品配置ID
                                box_id: item.box_id,
                                type: item.type, // 1=随机, 2=固定
                                claimed_count: item.claimed_count || 0,
                                products: item.items_info.map(product => ({
                                    id: product.short_code, // 使用简码作为ID
                                    name: product.name,
                                    short_code: product.short_code,
                                    quantity: product.num,
                                    // 由于API没有返回图片，使用占位图
                                    image:
                                        "https://via.placeholder.com/40x40/cccccc/ffffff?text=" +
                                        encodeURIComponent(
                                            product.name.charAt(0)
                                        )
                                }))
                            })
                        );
                    } else {
                        this.cycleDetailData = [];
                    }
                } else {
                    this.$message.error(
                        response.data.error_msg || "获取盲盒详情失败"
                    );
                    this.cycleDetailData = [];
                    this.currentBlindBox = {};
                }
            } catch (error) {
                console.error("获取盲盒详情失败:", error);
                this.$message.error("获取盲盒详情失败，请稍后重试");
                this.cycleDetailData = [];
                this.currentBlindBox = {};
            }
        },
        // 绑定商品
        async bindProducts(row) {
            this.bindProductDialogVisible = true;

            try {
                // 调用盲盒详情API获取完整信息，不使用列表数据
                const response = await this.$request.main.getBlindBoxDetail({
                    id: row.id
                });

                if (response.data.error_code === 0) {
                    const detailData = response.data.data;

                    // 使用接口返回的完整数据更新当前绑定盲盒信息
                    this.currentBindBlindBox = {
                        id: detailData.id,
                        name: detailData.title,
                        title: detailData.title,
                        avatar_image: detailData.avatar_image,
                        cover_image: detailData.avatar_image,
                        valid_time_unit: detailData.valid_time_unit,
                        valid_time_num: detailData.valid_time_num,
                        cycle: detailData.valid_time_num,
                        cycle_unit: detailData.valid_time_unit,
                        price: detailData.price,
                        status:
                            detailData.onsale_status === 2
                                ? "active"
                                : "inactive",
                        create_num: detailData.create_num,
                        active_num: detailData.active_num,
                        activated_count: detailData.active_num,
                        get_num: detailData.get_num,
                        claimed_count: detailData.get_num,
                        created_time: detailData.created_time,
                        created_at: detailData.created_time,
                        update_time: detailData.update_time
                    };

                    // 根据已有的商品配置生成绑定表单
                    this.generateBindFormFromDetail(detailData);
                } else {
                    this.$message.error(
                        response.data.error_msg || "获取盲盒详情失败"
                    );
                    // 如果获取详情失败，生成空的表单
                    this.currentBindBlindBox = { id: row.id };
                    this.generateEmptyBindForm();
                }
            } catch (error) {
                console.error("获取盲盒详情失败:", error);
                this.$message.error("获取盲盒详情失败，请稍后重试");
                // 如果获取详情失败，生成空的表单
                this.currentBindBlindBox = { id: row.id };
                this.generateEmptyBindForm();
            }
        },
        // 根据盲盒详情生成绑定表单
        generateBindFormFromDetail(detailData) {
            const cycleCount =
                this.currentBindBlindBox.cycle ||
                this.currentBindBlindBox.valid_time_num ||
                1;

            // 如果有已存在的商品配置，则基于现有配置生成表单
            if (detailData.items && detailData.items.length > 0) {
                // 创建一个映射，将现有配置按cycle_number索引
                const existingConfigsMap = {};
                detailData.items.forEach(item => {
                    existingConfigsMap[
                        item.cycle_number || detailData.items.indexOf(item) + 1
                    ] = item;
                });

                // 根据周期数量生成配置
                const cycles = [];
                for (let i = 1; i <= cycleCount; i++) {
                    const existingConfig = existingConfigsMap[i];
                    if (existingConfig) {
                        // 使用现有配置
                        cycles.push({
                            cycle_number: i,
                            item_id: existingConfig.id,
                            product_type:
                                existingConfig.type === 1 ? "random" : "fixed",
                            product_input: "",
                            products: existingConfig.items_info.map(
                                product => ({
                                    id: product.short_code,
                                    name: product.name,
                                    short_code: product.short_code,
                                    quantity: product.num,
                                    image:
                                        "https://via.placeholder.com/40x40/cccccc/ffffff?text=" +
                                        encodeURIComponent(
                                            product.name.charAt(0)
                                        )
                                })
                            )
                        });
                    } else {
                        // 创建空配置
                        cycles.push({
                            cycle_number: i,
                            item_id: null,
                            product_type: "random",
                            product_input: "",
                            products: []
                        });
                    }
                }

                this.bindProductForm = { cycles };
            } else {
                // 如果没有商品配置，生成空的配置
                this.generateEmptyBindForm();
            }
        },
        // 生成空的绑定表单
        generateEmptyBindForm() {
            const cycleCount =
                this.currentBindBlindBox.cycle ||
                this.currentBindBlindBox.valid_time_num ||
                1;
            const cycles = [];

            for (let i = 1; i <= cycleCount; i++) {
                cycles.push({
                    cycle_number: i,
                    item_id: null, // 新增时为null
                    product_type: "random",
                    product_input: "",
                    products: []
                });
            }

            this.bindProductForm = { cycles };
        },
        // 生成周期绑定表单
        generateCycleBindForm(blindBox) {
            const cycles = [];
            const currentDate = new Date();

            // 根据盲盒的周期设置生成对应的周期
            for (let i = 1; i <= 6; i++) {
                // 生成6个周期作为示例
                let startDate, endDate;

                if (blindBox.cycle_unit === "week") {
                    // 按周计算
                    const weekOffset = (i - 1) * blindBox.cycle * 7;
                    startDate = new Date(
                        currentDate.getTime() + weekOffset * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(
                        startDate.getTime() +
                            (blindBox.cycle * 7 - 1) * 24 * 60 * 60 * 1000
                    );
                } else {
                    // 按月计算
                    startDate = new Date(
                        currentDate.getFullYear(),
                        currentDate.getMonth() + (i - 1) * blindBox.cycle,
                        1
                    );
                    endDate = new Date(
                        currentDate.getFullYear(),
                        currentDate.getMonth() + i * blindBox.cycle,
                        0
                    );
                }

                cycles.push({
                    cycle_number: i,
                    start_time: this.formatDate(startDate),
                    end_time: this.formatDate(endDate),
                    claimed_count:
                        this.getExistingClaimedCount(blindBox.id, i) || 0,
                    product_type: "random", // 新增商品类型，默认为随机
                    product_input: "",
                    products: this.getExistingProducts(blindBox.id, i) || []
                });
            }

            this.bindProductForm = { cycles };
        },
        // 获取已存在的商品简码
        getExistingProductCodes(blindBoxId, cycleNumber) {
            const existingData = this.mockCycleDetails[blindBoxId];
            if (existingData) {
                const cycle = existingData.find(
                    c => c.cycle_number === cycleNumber
                );
                if (cycle && cycle.products) {
                    return cycle.products.map(p => p.short_code);
                }
            }
            return [];
        },
        // 获取已存在的商品（包含数量）
        getExistingProducts(blindBoxId, cycleNumber) {
            const existingData = this.mockCycleDetails[blindBoxId];
            if (existingData) {
                const cycle = existingData.find(
                    c => c.cycle_number === cycleNumber
                );
                if (cycle && cycle.products) {
                    return cycle.products.map(p => ({
                        ...p,
                        quantity: p.quantity || 1
                    }));
                }
            }
            return [];
        },
        // 获取已存在的已领取人数
        getExistingClaimedCount(blindBoxId, cycleNumber) {
            const existingData = this.mockCycleDetails[blindBoxId];
            if (existingData) {
                const cycle = existingData.find(
                    c => c.cycle_number === cycleNumber
                );
                if (cycle) {
                    return cycle.claimed_count || 0;
                }
            }
            return 0;
        },
        // 格式化日期
        formatDate(date) {
            return date
                .toISOString()
                .slice(0, 19)
                .replace("T", " ");
        },
        // 根据商品简码获取商品名称
        getProductNameByCode(code) {
            const product = this.availableProducts.find(
                p => p.short_code === code
            );
            return product ? product.name : null;
        },
        // 从输入框添加商品
        async addProductsFromInput(cycleIndex) {
            const cycle = this.bindProductForm.cycles[cycleIndex];
            const input = cycle.product_input.trim();

            if (!input) {
                this.$message.warning("请输入商品简码");
                return;
            }

            try {
                // 解析输入格式：简码*数量+简码*数量
                const productEntries = input
                    .split("+")
                    .map(entry => entry.trim());

                let addedCount = 0; // 记录成功添加的商品数量

                for (const entry of productEntries) {
                    if (!entry) continue;

                    let shortCode,
                        quantity = 1;

                    if (entry.includes("*")) {
                        const parts = entry.split("*");
                        if (parts.length !== 2) {
                            throw new Error(`格式错误: ${entry}`);
                        }
                        shortCode = parts[0].trim();
                        quantity = parseInt(parts[1].trim());

                        if (isNaN(quantity) || quantity < 1) {
                            throw new Error(`数量必须是大于0的整数: ${entry}`);
                        }
                    } else {
                        shortCode = entry;
                    }

                    if (!shortCode) {
                        throw new Error(`商品简码不能为空: ${entry}`);
                    }

                    // 检查是否已存在该商品
                    const existingIndex = cycle.products.findIndex(
                        p => p.short_code === shortCode
                    );

                    if (existingIndex > -1) {
                        // 如果已存在，累加数量
                        cycle.products[existingIndex].quantity += quantity;
                        addedCount++; // 累加数量也算作成功添加
                    } else {
                        // 调用商品信息接口获取商品详情
                        let productName = null;
                        let productFound = false;

                        try {
                            const params = {
                                short_code: shortCode
                            };
                            const res = await this.$request.main.queryProductByShortCode(
                                params
                            );

                            if (
                                res.data.error_code === 0 &&
                                res.data.data &&
                                res.data.data.length > 0
                            ) {
                                const productInfo = res.data.data[0];
                                productName =
                                    productInfo.cn_product_name || shortCode;
                                productFound = true;
                            } else {
                                // 查询结果为空，商品不存在
                                this.$message.error(
                                    `商品简码 "${shortCode}" 不存在，请检查后重新输入`
                                );
                                continue; // 跳过这个商品，继续处理下一个
                            }
                        } catch (error) {
                            console.warn(
                                `查询商品信息失败: ${shortCode}`,
                                error
                            );
                            this.$message.error(
                                `查询商品信息失败: ${shortCode}，请重试`
                            );
                            continue; // 跳过这个商品，继续处理下一个
                        }

                        // 只有当商品存在时才添加
                        if (productFound && productName) {
                            cycle.products.push({
                                id: Date.now() + Math.random(),
                                name: productName,
                                short_code: shortCode,
                                image:
                                    "https://via.placeholder.com/60x60/cccccc/ffffff?text=" +
                                    encodeURIComponent(productName.charAt(0)),
                                quantity: quantity
                            });
                            addedCount++; // 成功添加新商品
                        }
                    }
                }

                // 清空输入框
                cycle.product_input = "";

                // 根据添加结果显示相应消息
                if (addedCount > 0) {
                    this.$message.success(`成功添加 ${addedCount} 个商品`);
                }
            } catch (error) {
                this.$message.error(`输入格式错误: ${error.message}`);
            }
        },
        // 移除商品
        removeProduct(cycleIndex, productIndex) {
            const cycle = this.bindProductForm.cycles[cycleIndex];
            cycle.products.splice(productIndex, 1);
            this.$message.success("商品已移除");
        },
        // 提交绑定商品
        async submitBindProducts() {
            try {
                const blindBoxId = this.currentBindBlindBox.id;

                // 构建API请求数据，包含配置ID
                const apiData = {
                    id: blindBoxId,
                    items: this.bindProductForm.cycles.map(cycle => {
                        const itemData = {
                            type: cycle.product_type === "random" ? 1 : 2, // 1=随机, 2=固定
                            items_info: cycle.products.map(product => ({
                                name: product.name,
                                short_code: product.short_code,
                                num: product.quantity
                            }))
                        };

                        // 如果存在配置ID，则包含在请求中（用于更新现有配置）
                        if (cycle.item_id) {
                            itemData.id = cycle.item_id;
                        }

                        return itemData;
                    })
                };

                console.log("绑定商品请求数据:", apiData);

                // 调用绑定商品API
                const response = await this.$request.main.bindGoodsToBlindBox(
                    apiData
                );

                if (response.data.error_code === 0) {
                    this.$message.success("商品绑定成功");
                    this.bindProductDialogVisible = false;
                    // 刷新盲盒列表
                    this.getBlindBoxList();
                } else {
                    this.$message.error(
                        response.data.error_msg || "商品绑定失败"
                    );
                }
            } catch (error) {
                console.error("绑定商品失败:", error);
                this.$message.error("绑定商品失败，请稍后重试");
            }
        },

        // 切换盲盒状态（上架/下架）
        async toggleBlindBoxStatus(row) {
            const newStatus = row.status === "active" ? "inactive" : "active";
            const statusText = newStatus === "active" ? "上架" : "下架";

            this.$confirm(`确认要${statusText}该盲盒吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(async () => {
                    try {
                        // 调用API设置盲盒上下架状态
                        const onsaleStatus = newStatus === "active" ? 2 : 3;
                        const response = await this.$request.main.setBlindBoxOnsaleStatus(
                            {
                                id: row.id,
                                onsale_status: onsaleStatus
                            }
                        );

                        if (response.data.error_code === 0) {
                            this.$message.success(`${statusText}成功`);
                            // 刷新列表
                            this.getBlindBoxList();
                        } else {
                            this.$message.error(
                                response.data.error_msg || `${statusText}失败`
                            );
                        }
                    } catch (error) {
                        console.error(`${statusText}盲盒失败:`, error);
                        this.$message.error(`${statusText}失败，请稍后重试`);
                    }
                })
                .catch(() => {
                    // 用户取消操作
                });
        },

        // 获取周期单位文本
        getCycleUnitText(unit) {
            const unitMap = {
                hour: "小时",
                day: "天",
                week: "周",
                month: "月",
                year: "年"
            };
            return unitMap[unit] || unit;
        },

        // 获取周期单位标签类型
        getCycleUnitTagType(unit) {
            const typeMap = {
                hour: "info",
                day: "warning",
                week: "primary",
                month: "success",
                year: "danger"
            };
            return typeMap[unit] || "info";
        }
    },
    watch: {
        dateRange: {
            handler: "handleDateRangeChange",
            immediate: true
        }
    }
};
</script>

<style scoped>
.blind-box-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    margin-bottom: 20px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.blind-box-detail {
    padding: 10px 0;
}

.detail-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.detail-header h3 {
    margin: 0 0 10px 0;
    color: #303133;
}

.detail-header p {
    margin: 0;
    color: #909399;
    font-size: 14px;
}

.product-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
}

.product-code {
    font-size: 12px;
    color: #909399;
}

/* 绑定商品对话框样式 */
.bind-product-content {
    padding: 10px 0;
}

.bind-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.bind-header h3 {
    margin: 0 0 10px 0;
    color: #303133;
}

.bind-header p {
    margin: 0;
    color: #909399;
    font-size: 14px;
}

.cycle-bind-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.cycle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.cycle-header h4 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.cycle-time {
    color: #6c757d;
    font-size: 13px;
}

.selected-products {
    margin-top: 15px;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.selected-products-title {
    margin-bottom: 10px;
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
}

.form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #f56c6c;
    line-height: 1.4;
    background: #fef0f0;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #f56c6c;
}

/* 输入框提示样式 */
.input-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
}

/* 商品列表容器样式（绑定商品弹窗专用） */
.bind-product-content .product-list-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bind-product-content .selected-product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background-color: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.bind-product-content .selected-product-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.bind-product-content .product-info-section {
    flex: 1;
    min-width: 0;
}

.bind-product-content .product-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
}

.bind-product-content .product-code {
    font-size: 12px;
    color: #909399;
}

.bind-product-content .product-code-quantity {
    display: flex;
    align-items: center;
    margin-top: 8px;
    gap: 12px;
}

.bind-product-content .quantity-label {
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
}

.bind-product-content .quantity-unit {
    font-size: 12px;
    color: #909399;
    margin-left: 4px;
}

.bind-product-content .product-quantity-section {
    display: flex;
    align-items: center;
    margin: 0 15px;
}

.bind-product-content .quantity-label {
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
}

.bind-product-content .product-actions {
    display: flex;
    align-items: center;
}

/* 帮助图标样式 */
.help-icon {
    margin-left: 6px;
    color: #909399;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.help-icon:hover {
    color: #409eff;
}

/* 盲盒详情弹窗样式 */
.blind-box-detail-dialog .el-dialog__body {
    padding: 20px 24px;
}

.blind-box-detail {
    padding: 0;
}

/* 概览区域样式 */
.detail-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    color: white;
}

/* 封面图片区域 */
.cover-section {
    position: relative;
}

.cover-image-container {
    width: 100%;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cover-image {
    width: 100%;
    height: 100%;
}

.cover-image .image-slot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.cover-image .image-slot i {
    font-size: 32px;
    margin-bottom: 8px;
}

.cover-image .image-slot p {
    margin: 0;
    font-size: 14px;
}

.status-badge {
    position: absolute;
    top: 12px;
    right: 12px;
}

/* 信息区域样式 */
.info-section {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.title-section {
    margin-bottom: 20px;
}

.blind-box-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: white;
    line-height: 1.3;
}

.title-meta {
    display: flex;
    align-items: center;
    gap: 16px;
}

.blind-box-id {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.price-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    color: white;
}

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.stat-icon.create {
    background: rgba(103, 194, 58, 0.2);
    color: #67c23a;
}

.stat-icon.active {
    background: rgba(64, 158, 255, 0.2);
    color: #409eff;
}

.stat-icon.claimed {
    background: rgba(230, 162, 60, 0.2);
    color: #e6a23c;
}

.stat-icon.cycle {
    background: rgba(245, 108, 108, 0.2);
    color: #f56c6c;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: white;
    line-height: 1.2;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 2px;
}

/* 时间信息样式 */
.time-info {
    margin-top: auto;
}

.time-item {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.time-item i {
    margin-right: 8px;
    font-size: 16px;
}

.time-label {
    margin-right: 4px;
}

.time-value {
    color: white;
    font-weight: 500;
}

/* 商品配置区域样式 */
.detail-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f2f5;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
}

.section-header h3 i {
    margin-right: 8px;
    color: #409eff;
    font-size: 20px;
}

.section-meta {
    font-size: 14px;
    color: #909399;
    background: #f0f2f5;
    padding: 4px 12px;
    border-radius: 12px;
}

/* 配置卡片样式 */
.product-configs {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.config-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.config-card:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.config-header {
    background: #f8f9fa;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
}

.config-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.config-number {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

/* 商品卡片样式 */
.config-products {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.product-card {
    display: flex;
    align-items: center;
    background: #fafbfc;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
}

.product-card:hover {
    background: #f0f9ff;
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 6px;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.product-code {
    font-size: 12px;
    color: #909399;
    background: #e4e7ed;
    padding: 2px 6px;
    border-radius: 4px;
}

.product-quantity {
    font-size: 12px;
    color: #67c23a;
    font-weight: 600;
    background: rgba(103, 194, 58, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* 绑定商品弹窗样式 */
.bind-product-content {
    padding: 10px 0;
}

.cycle-bind-section {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fafafa;
}

.cycle-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
}

.cycle-header h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.selected-products {
    margin-top: 15px;
}

.selected-products-title {
    font-weight: 500;
    color: #606266;
    margin-bottom: 10px;
}

/* 绑定商品弹窗中的商品列表样式 */
.bind-product-content .product-list-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.bind-product-content .selected-product-item {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
    min-width: 300px;
}

.bind-product-content .product-info-section {
    flex: 1;
    margin-right: 10px;
}

.bind-product-content .product-quantity-section {
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.bind-product-content .quantity-label {
    margin-left: 5px;
    color: #606266;
    font-size: 12px;
}

.bind-product-content .product-actions {
    display: flex;
    align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .blind-box-detail-dialog .el-dialog {
        width: 95% !important;
    }

    .config-products {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .detail-overview {
        padding: 16px;
    }

    .detail-overview .el-col {
        margin-bottom: 20px;
    }

    .cover-image-container {
        height: 150px;
    }

    .blind-box-title {
        font-size: 20px;
    }

    .stats-cards .el-col {
        margin-bottom: 12px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-number {
        font-size: 16px;
    }

    .config-products {
        grid-template-columns: 1fr;
        padding: 16px;
    }

    .product-card {
        padding: 12px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .blind-box-detail-dialog .el-dialog__body {
        padding: 16px;
    }

    .detail-overview {
        padding: 12px;
    }

    .detail-content {
        padding: 16px;
    }

    .title-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .time-info .el-col {
        margin-bottom: 8px;
    }
}
</style>
