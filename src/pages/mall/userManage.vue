<template>
    <div class="user-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="手机号">
                        <el-input
                            v-model="searchForm.telephone"
                            placeholder="手机号"
                            clearable
                            style="width: 180px;"
                        />
                    </el-form-item>
                    <el-form-item label="昵称">
                        <el-input
                            v-model="searchForm.nickname"
                            placeholder="昵称"
                            clearable
                            style="width: 150px;"
                        />
                    </el-form-item>
                    <el-form-item label="用户类型">
                        <el-select
                            v-model="searchForm.type"
                            placeholder="用户类型"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="普通用户" :value="1"></el-option>
                            <el-option label="会员" :value="2"></el-option>
                            <el-option label="合伙人" :value="3"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="合伙人状态">
                        <el-select
                            v-model="searchForm.level_status"
                            placeholder="合伙人状态"
                            clearable
                            style="width: 130px;"
                        >
                            <el-option label="无" :value="1"></el-option>
                            <el-option label="有效" :value="2"></el-option>
                            <el-option label="无效" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <span slot="label">
                            禁用状态
                            <el-tooltip
                                content='说明：状态为"无"表示从未签约过；状态为"无效"表示从合伙人降级到会员用户'
                                placement="top"
                                effect="dark"
                            >
                                <i
                                    class="el-icon-question"
                                    style="margin-left: 4px; color: #999; cursor: pointer;"
                                ></i>
                            </el-tooltip>
                        </span>
                        <el-select
                            v-model="searchForm.is_disabled"
                            placeholder="禁用状态"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="否" :value="1"></el-option>
                            <el-option label="是" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 统计信息 -->
        <div class="statistics-container">
            <el-card>
                <div class="statistics-info">
                    <div class="stat-item">
                        <div class="stat-number">{{ totalUsers }}</div>
                        <div class="stat-label">注册总人数</div>
                    </div>
                </div>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                >
                    <el-table-column label="昵称" width="220">
                        <template slot-scope="scope">
                            <div style="display: flex; align-items: center;">
                                <el-avatar
                                    :size="40"
                                    :src="scope.row.avatar_image"
                                    icon="el-icon-user-solid"
                                ></el-avatar>
                                <div
                                    style="margin-left: 10px; display: flex; flex-direction: column; align-items: flex-start;"
                                >
                                    <span>{{ scope.row.nickname }}</span>
                                    <el-tag
                                        :type="
                                            getMemberTypeTagType(scope.row.type)
                                        "
                                        size="mini"
                                        style="margin-top: 4px;"
                                    >
                                        {{ getMemberTypeText(scope.row.type) }}
                                    </el-tag>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="telephone"
                        label="手机号"
                        width="130"
                    ></el-table-column>

                    <el-table-column
                        label="openid"
                        show-overflow-tooltip
                        width="160"
                    >
                        <template slot-scope="scope">
                            <div style="display: flex; align-items: center;">
                                <span
                                    style="flex: 1; overflow: hidden; text-overflow: ellipsis;"
                                >
                                    {{ scope.row.applet_openid }}
                                </span>
                                <el-button
                                    type="text"
                                    size="mini"
                                    icon="el-icon-copy-document"
                                    @click="copyOpenId(scope.row.applet_openid)"
                                    style="margin-left: 5px; padding: 0; color: #409eff;"
                                    title="复制openid"
                                ></el-button>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="level_status" width="130">
                        <template slot="header">
                            <span
                                style="white-space: nowrap; display: inline-flex; align-items: center;"
                            >
                                合伙人状态
                                <el-tooltip
                                    content='状态为"有效"时代表用户当前为合伙人，当状态为"无效"时代表用户由合伙人降级为了普通会员，当状态为"无"时代表用户从未成为过合伙人。'
                                    placement="top"
                                    effect="dark"
                                >
                                    <i
                                        class="el-icon-question"
                                        style="margin-left: 4px; color: #999; cursor: pointer;"
                                    ></i>
                                </el-tooltip>
                            </span>
                        </template>
                        <template slot-scope="scope">
                            {{ getLevelStatusText(scope.row.level_status) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="total_balance"
                        label="累计礼金总额"
                        width="130"
                    >
                        <template slot-scope="scope">
                            <span style="color: #f56c6c; font-weight: bold;"
                                >¥{{ scope.row.total_balance || 0 }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="balance"
                        label="可用礼金余额"
                        width="130"
                    >
                        <template slot-scope="scope">
                            <span style="color: #67c23a; font-weight: bold;"
                                >¥{{ scope.row.balance || 0 }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="is_disabled"
                        label="禁用状态"
                        width="100"
                    >
                        <template slot-scope="scope">
                            {{ getDisabledStatusText(scope.row.is_disabled) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        label="注册时间"
                        width="180"
                    ></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button
                                size="mini"
                                :type="
                                    scope.row.is_disabled == 1
                                        ? 'warning'
                                        : 'success'
                                "
                                @click="toggleUserStatus(scope.row)"
                            >
                                {{
                                    scope.row.is_disabled == 1 ? "禁用" : "启用"
                                }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 新增/编辑用户对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form
                :model="userForm"
                :rules="userRules"
                ref="userForm"
                label-width="100px"
            >
                <el-form-item label="用户名" prop="username">
                    <el-input
                        v-model="userForm.username"
                        :disabled="isEdit"
                    ></el-input>
                </el-form-item>
                <el-form-item label="昵称" prop="nickname">
                    <el-input v-model="userForm.nickname"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="userForm.phone"></el-input>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="userForm.email"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password" v-if="!isEdit">
                    <el-input
                        v-model="userForm.password"
                        type="password"
                    ></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="userForm.status">
                        <el-radio :label="1">正常</el-radio>
                        <el-radio :label="0">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUser">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "UserManage",
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            totalUsers: 0, // 注册总人数
            searchForm: {
                page: 1,
                limit: 10,
                telephone: "",
                nickname: "",
                level: "",
                type: "",
                level_status: "",
                is_disabled: ""
            },
            dialogVisible: false,
            dialogTitle: "新增用户",
            isEdit: false,
            userForm: {
                id: "",
                username: "",
                nickname: "",
                phone: "",
                email: "",
                password: "",
                status: 1
            },
            userRules: {
                username: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur"
                    },
                    {
                        min: 3,
                        max: 20,
                        message: "长度在 3 到 20 个字符",
                        trigger: "blur"
                    }
                ],
                nickname: [
                    { required: true, message: "请输入昵称", trigger: "blur" }
                ],
                phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: "请输入正确的手机号",
                        trigger: "blur"
                    }
                ],
                email: [
                    {
                        type: "email",
                        message: "请输入正确的邮箱地址",
                        trigger: "blur"
                    }
                ],
                password: [
                    { required: true, message: "请输入密码", trigger: "blur" },
                    {
                        min: 6,
                        max: 20,
                        message: "长度在 6 到 20 个字符",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getUserList();
        this.getTotalUsers();
    },
    methods: {
        // 获取用户列表
        async getUserList() {
            this.loading = true;
            try {
                // 过滤空值参数
                const params = {};
                Object.keys(this.searchForm).forEach(key => {
                    if (
                        this.searchForm[key] !== "" &&
                        this.searchForm[key] !== null &&
                        this.searchForm[key] !== undefined
                    ) {
                        params[key] = this.searchForm[key];
                    }
                });

                const res = await this.$request.main.getUserList(params);
                console.log("用户列表", res);

                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                    this.totalUsers = res.data.data.total_user;
                } else {
                    this.$message.error(
                        res.data.error_msg || "获取用户列表失败"
                    );
                }
            } catch (error) {
                console.error("获取用户列表失败:", error);
                this.$message.error("获取用户列表失败");
            } finally {
                this.loading = false;
            }
        },
        // 获取注册总人数（已在getUserList中获取）
        async getTotalUsers() {
            // 总用户数已在getUserList接口中返回，无需单独调用
        },
        // 获取用户类型文本
        getMemberTypeText(type) {
            const typeMap = {
                1: "普通用户",
                2: "会员",
                3: "合伙人"
            };
            return typeMap[type] || "未知";
        },
        // 获取用户类型标签颜色
        getMemberTypeTagType(type) {
            const typeMap = {
                1: "", // 普通用户 - 默认灰色
                2: "success", // 会员 - 绿色
                3: "warning" // 合伙人 - 橙色
            };
            return typeMap[type] || "";
        },
        // 获取合伙人状态文本
        getLevelStatusText(status) {
            const statusMap = {
                1: "无",
                2: "有效",
                3: "无效"
            };
            return statusMap[status] || "未知";
        },
        // 获取禁用状态文本
        getDisabledStatusText(status) {
            const statusMap = {
                1: "否",
                2: "是"
            };
            return statusMap[status] || "未知";
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getUserList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                telephone: "",
                nickname: "",
                level: "",
                type: "",
                level_status: "",
                is_disabled: ""
            };
            this.getUserList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getUserList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getUserList();
        },
        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = "新增用户";
            this.isEdit = false;
            this.userForm = {
                id: "",
                username: "",
                nickname: "",
                phone: "",
                email: "",
                password: "",
                status: 1
            };
            this.dialogVisible = true;
        },
        // 编辑用户
        editUser(row) {
            this.dialogTitle = "编辑用户";
            this.isEdit = true;
            this.userForm = {
                id: row.id,
                username: row.username,
                nickname: row.nickname,
                phone: row.phone,
                email: row.email,
                password: "",
                status: row.status
            };
            this.dialogVisible = true;
        },
        // 提交用户信息
        submitUser() {
            this.$refs.userForm.validate(valid => {
                if (valid) {
                    // 模拟提交
                    this.$message.success(
                        this.isEdit ? "编辑成功" : "新增成功"
                    );
                    this.dialogVisible = false;
                    this.getUserList();
                }
            });
        },
        // 切换用户状态
        async toggleUserStatus(row) {
            const action = row.is_disabled == 1 ? "禁用" : "启用";
            const newStatus = row.is_disabled == 1 ? 2 : 1; // 1=否(启用), 2=是(禁用)

            this.$confirm(`确定要${action}该用户吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    const res = await this.$request.main.setUserDisabled({
                        id: row.id,
                        is_disabled: newStatus
                    });

                    if (res.data.error_code === 0) {
                        this.$message.success(`${action}成功`);
                        this.getUserList(); // 重新获取列表
                    } else {
                        this.$message.error(
                            res.data.error_msg || `${action}失败`
                        );
                    }
                } catch (error) {
                    console.error(`${action}用户失败:`, error);
                    this.$message.error(`${action}失败`);
                }
            });
        },
        // 删除用户
        deleteUser() {
            this.$confirm("确定要删除该用户吗？删除后不可恢复！", "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                // 模拟删除
                this.$message.success("删除成功");
                this.getUserList();
            });
        },
        // 复制openid
        async copyOpenId(openid) {
            if (!openid) {
                this.$message.warning("openid为空，无法复制");
                return;
            }

            try {
                // 优先使用现代 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(openid);
                    this.$message.success("openid已复制到剪贴板");
                } else {
                    // 降级方案：使用传统方法
                    const textArea = document.createElement("textarea");
                    textArea.value = openid;
                    textArea.style.position = "fixed";
                    textArea.style.left = "-999999px";
                    textArea.style.top = "-999999px";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    const successful = document.execCommand("copy");
                    document.body.removeChild(textArea);

                    if (successful) {
                        this.$message.success("openid已复制到剪贴板");
                    } else {
                        throw new Error("复制命令执行失败");
                    }
                }
            } catch (err) {
                console.error("复制失败:", err);
                this.$message.error("复制失败，请手动复制");
            }
        }
    }
};
</script>

<style scoped>
.user-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.statistics-container {
    margin-bottom: 20px;
}

.statistics-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.table-container {
    background: #fff;
}
</style>
