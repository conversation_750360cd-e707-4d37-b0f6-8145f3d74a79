<template>
    <div class="banner-manage-layout">
        <div class="search-form">
            <el-card>
                <el-form
                    :inline="true"
                    :model="searchForm"
                    class="demo-form-inline"
                >
                    <el-form-item label="Banner标题">
                        <el-input
                            v-model="searchForm.title"
                            placeholder="Banner标题"
                            clearable
                            style="width: 200px;"
                        />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            v-model="searchForm.status"
                            placeholder="状态"
                            clearable
                            style="width: 120px;"
                        >
                            <el-option label="启用" value="2"></el-option>
                            <el-option label="禁用" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search"
                            >查询</el-button
                        >
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="success" @click="showAddDialog"
                            >新增Banner</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <div class="table-container">
            <el-card>
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                    row-key="id"
                >
                    <el-table-column
                        prop="id"
                        label="ID"
                        width="80"
                    ></el-table-column>
                    <el-table-column
                        prop="picture"
                        label="Banner图片"
                        width="150"
                    >
                        <template slot-scope="scope">
                            <el-image
                                style="width: 120px; height: 60px"
                                :src="scope.row.picture"
                                :preview-src-list="[scope.row.picture]"
                                fit="cover"
                            >
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                </div>
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="标题"
                        width="200"
                    ></el-table-column>
                    <el-table-column
                        prop="jump_type"
                        label="跳转方式"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <el-tag
                                :type="getJumpTypeColor(scope.row.jump_type)"
                            >
                                {{ getJumpTypeName(scope.row.jump_type) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="jump_value"
                        label="跳转值"
                        width="200"
                        show-overflow-tooltip
                    >
                        <template slot-scope="scope">
                            {{ scope.row.jump_value || "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sort"
                        label="排序"
                        width="80"
                    ></el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag
                                :type="
                                    scope.row.status == 2 ? 'success' : 'danger'
                                "
                            >
                                {{ scope.row.status == 2 ? "启用" : "禁用" }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        width="180"
                    ></el-table-column>
                    <el-table-column label="操作" width="220">
                        <template slot-scope="scope">
                            <el-button
                                size="mini"
                                @click="editBanner(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                size="mini"
                                :type="
                                    scope.row.status == 2
                                        ? 'warning'
                                        : 'success'
                                "
                                @click="toggleBannerStatus(scope.row)"
                            >
                                {{ scope.row.status == 2 ? "禁用" : "启用" }}
                            </el-button>
                            <el-button
                                size="mini"
                                type="danger"
                                @click="deleteBanner(scope.row)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="searchForm.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 新增/编辑Banner对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="700px"
            :close-on-click-modal="false"
        >
            <el-form
                :model="bannerForm"
                :rules="bannerRules"
                ref="bannerForm"
                label-width="100px"
            >
                <el-form-item label="Banner标题" prop="name">
                    <el-input v-model="bannerForm.name"></el-input>
                </el-form-item>
                <el-form-item label="Banner图片" prop="picture">
                    <vos-oss
                        :key="uploadKey"
                        list-type="picture-card"
                        :showFileList="true"
                        :dir="dir"
                        :file-list="bannerForm.picture"
                        :limit="1"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                    <div class="upload-tip">
                        建议尺寸：750x610，支持JPG、PNG格式，大小不超过2MB
                    </div>
                </el-form-item>
                <el-form-item label="跳转方式" prop="jump_type">
                    <el-radio-group v-model="bannerForm.jump_type">
                        <el-radio :label="1">链接</el-radio>
                        <el-radio :label="2">页面</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="跳转值" prop="jump_value">
                    <el-input
                        v-model="bannerForm.jump_value"
                        placeholder="请输入跳转值（链接地址或页面标识）"
                    ></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number
                        v-model="bannerForm.sort"
                        :min="0"
                        style="width: 200px;"
                    ></el-input-number>
                    <div class="form-tip">数字越小排序越靠前</div>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="bannerForm.status">
                        <el-radio :label="2">启用</el-radio>
                        <el-radio :label="1">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitBanner">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";

export default {
    name: "BannerManage",
    components: {
        vosOss
    },
    data() {
        return {
            loading: false,
            tableData: [],
            total: 0,
            dir: "vinehoo/mulando-hotel/banner/",
            uploadKey: Date.now(), // 用于强制重新渲染上传组件
            searchForm: {
                page: 1,
                limit: 10,
                title: "",
                status: ""
            },
            dialogVisible: false,
            dialogTitle: "新增Banner",
            isEdit: false,
            bannerForm: {
                id: "",
                name: "",
                picture: [],
                jump_type: 1,
                jump_value: "",
                sort: 1000,
                status: 2
            },
            bannerRules: {
                name: [
                    {
                        required: true,
                        message: "请输入Banner标题",
                        trigger: "blur"
                    }
                ],
                picture: [
                    {
                        required: true,
                        message: "请上传Banner图片",
                        trigger: "change"
                    }
                ],
                jump_value: [
                    {
                        required: true,
                        message: "请输入跳转值",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getBannerList();
    },
    methods: {
        // 获取Banner列表
        async getBannerList() {
            this.loading = true;
            try {
                // 过滤空值参数
                const params = {};
                Object.keys(this.searchForm).forEach(key => {
                    if (
                        this.searchForm[key] !== "" &&
                        this.searchForm[key] !== null &&
                        this.searchForm[key] !== undefined
                    ) {
                        params[key] = this.searchForm[key];
                    }
                });

                const res = await this.$request.main.getBannerList(params);
                console.log("Banner列表", res);

                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                }
            } catch (error) {
                console.error("获取Banner列表失败:", error);
                this.$message.error("获取Banner列表失败");
            } finally {
                this.loading = false;
            }
        },
        // 搜索
        search() {
            this.searchForm.page = 1;
            this.getBannerList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                page: 1,
                limit: 10,
                title: "",
                status: ""
            };
            this.getBannerList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.searchForm.limit = val;
            this.searchForm.page = 1;
            this.getBannerList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.searchForm.page = val;
            this.getBannerList();
        },
        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = "新增Banner";
            this.isEdit = false;
            // 生成新的uploadKey来强制重新渲染上传组件，清除图片残留
            this.uploadKey = Date.now();
            this.bannerForm = {
                id: "",
                name: "",
                picture: [],
                jump_type: 1,
                jump_value: "",
                sort: 1000,
                status: 2
            };
            // 确保表单验证状态也被重置
            this.$nextTick(() => {
                if (this.$refs.bannerForm) {
                    this.$refs.bannerForm.clearValidate();
                }
            });
            this.dialogVisible = true;
        },
        // 编辑Banner
        editBanner(row) {
            this.dialogTitle = "编辑Banner";
            this.isEdit = true;
            // 编辑时也生成新的uploadKey，确保组件正确渲染
            this.uploadKey = Date.now();
            this.bannerForm = {
                id: row.id,
                name: row.name,
                picture: row.picture ? [row.picture] : [],
                jump_type: row.jump_type,
                jump_value: row.jump_value || "",
                sort: row.sort,
                status: row.status
            };
            this.dialogVisible = true;
        },
        // 提交Banner信息
        submitBanner() {
            this.$refs.bannerForm.validate(async valid => {
                if (valid) {
                    try {
                        let res;
                        if (this.isEdit) {
                            // 编辑Banner时，构造请求数据
                            const editData = {
                                id: this.bannerForm.id,
                                name: this.bannerForm.name,
                                picture:
                                    this.bannerForm.picture.length > 0
                                        ? this.bannerForm.picture[0]
                                        : "",
                                jump_type: this.bannerForm.jump_type,
                                jump_value: this.bannerForm.jump_value,
                                sort: this.bannerForm.sort,
                                status: this.bannerForm.status
                            };
                            res = await this.$request.main.editBanner(editData);
                        } else {
                            // 创建Banner时，构造请求数据
                            const createData = {
                                name: this.bannerForm.name,
                                picture:
                                    this.bannerForm.picture.length > 0
                                        ? this.bannerForm.picture[0]
                                        : "",
                                jump_type: this.bannerForm.jump_type,
                                jump_value: this.bannerForm.jump_value,
                                sort: this.bannerForm.sort,
                                status: this.bannerForm.status
                            };
                            res = await this.$request.main.createBanner(
                                createData
                            );
                        }

                        if (res.data.error_code === 0) {
                            this.$message.success(
                                this.isEdit ? "编辑成功" : "新增成功"
                            );
                            this.dialogVisible = false;
                            this.getBannerList();
                        }
                    } catch (error) {
                        this.$message.error("网络错误，请稍后重试");
                    }
                }
            });
        },
        // 切换Banner状态
        toggleBannerStatus(row) {
            const action = row.status == 2 ? "禁用" : "启用";
            this.$confirm(`确定要${action}该Banner吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    const res = await this.$request.main.editBanner({
                        id: row.id,
                        name: row.name,
                        picture: row.picture,
                        jump_type: row.jump_type,
                        jump_value: row.jump_value,
                        sort: row.sort,
                        status: row.status == 2 ? 1 : 2
                    });

                    if (res.data.error_code === 0) {
                        this.$message.success(`${action}成功`);
                        this.getBannerList();
                    }
                } catch (error) {
                    this.$message.error("网络错误，请稍后重试");
                }
            });
        },
        // 删除Banner
        deleteBanner(row) {
            this.$confirm(`确定要删除Banner "${row.name}" 吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    const res = await this.$request.main.deleteBanner({
                        id: row.id
                    });

                    if (res.data.error_code === 0) {
                        this.$message.success("删除成功");
                        this.getBannerList();
                    }
                } catch (error) {
                    this.$message.error("网络错误，请稍后重试");
                }
            });
        },

        // 获取跳转方式名称
        getJumpTypeName(type) {
            const typeMap = {
                1: "链接",
                2: "页面"
            };
            return typeMap[type] || "未知";
        },
        // 获取跳转方式颜色
        getJumpTypeColor(type) {
            const colorMap = {
                1: "success",
                2: "primary"
            };
            return colorMap[type] || "info";
        }
    }
};
</script>

<style scoped>
.banner-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.table-container {
    background: #fff;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 30px;
}

.upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
}

.form-tip {
    color: #999;
    font-size: 12px;
    margin-left: 10px;
}
</style>
