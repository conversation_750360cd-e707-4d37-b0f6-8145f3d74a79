<template>
    <div class="shipping-management-page">
        <!-- 搜索筛选区域 -->
        <el-card class="filter-card" shadow="never">
            <el-form
                label-position="top"
                :model="searchForm"
                :inline="true"
                class="search-form"
            >
                <el-form-item label="搜索">
                    <el-input
                        v-model="searchForm.keyword"
                        placeholder="公司名称/商品名称/收货人/简码"
                        clearable
                        @keyup.enter.native="handleSearch"
                        style="width: 280px;"
                    />
                </el-form-item>
                <el-form-item label="发货状态">
                    <el-select
                        v-model="searchForm.status"
                        placeholder="选择状态"
                        clearable
                    >
                        <el-option label="待发货" :value="1" />
                        <el-option label="已发货" :value="2" />
                        <el-option label="已签收" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申请时间">
                    <el-date-picker
                        v-model="searchForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 380px;"
                    />
                </el-form-item>
                <el-form-item label="操作">
                    <div>
                        <el-button type="primary" @click="handleSearch"
                            >搜索</el-button
                        >
                        <el-button @click="handleReset">重置</el-button>
                    </div>
                </el-form-item>
                <el-row :gutter="16">
                    <el-col :xs="24" :sm="12" :md="6"> </el-col>
                    <el-col :xs="24" :sm="12" :md="6"> </el-col>
                    <el-col :xs="24" :sm="12" :md="8"> </el-col>
                    <el-col :xs="24" :sm="12" :md="4"> </el-col>
                </el-row>
            </el-form>
        </el-card>

        <!-- 数据表格 -->
        <el-card class="table-card" shadow="never">
            <el-table
                :data="tableData"
                v-loading="loading"
                stripe
                border
                height="calc(100vh - 300px)"
                @sort-change="handleSortChange"
            >
                <el-table-column
                    prop="company_name"
                    label="公司名称"
                    width="200"
                    show-overflow-tooltip
                />

                <el-table-column
                    prop="goods_name"
                    label="商品信息"
                    width="180"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <div>
                            <div class="product-name">
                                {{ scope.row.goods_name }}
                            </div>
                            <div class="product-code">
                                {{ scope.row.short_code }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="redeem_quantity"
                    label="兑换数量"
                    width="100"
                    align="center"
                />

                <el-table-column
                    prop="consignee"
                    label="收货人"
                    width="130"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <div>
                            <div class="recipient-name">
                                {{ scope.row.consignee }}
                            </div>
                            <div class="recipient-phone">
                                {{ maskPhone(scope.row.contact_phone) }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    label="收货地址"
                    min-width="250"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ getFullAddress(scope.row) }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="create_time"
                    label="申请时间"
                    width="160"
                    sortable="custom"
                    show-overflow-tooltip
                />

                <el-table-column
                    prop="status"
                    label="发货状态"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            :type="getStatusTagType(scope.row.status)"
                            size="small"
                        >
                            {{ getStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="shipping_code"
                    label="物流单号"
                    width="150"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.shipping_code || "-" }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="ship_time"
                    label="发货时间"
                    width="160"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.ship_time || "-" }}
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                />
            </div>
        </el-card>
    </div>
</template>

<script>
export default {
    name: "ShippingManagement",
    data() {
        return {
            loading: false,

            // 搜索表单
            searchForm: {
                keyword: "",
                status: null,
                dateRange: null
            },

            // 分页
            pagination: {
                page: 1,
                limit: 10,
                total: 0
            },

            // 数据
            tableData: [],

            // 发货状态映射
            statusMap: {
                1: { text: "待发货", type: "warning" },
                2: { text: "已发货", type: "primary" },
                3: { text: "已签收", type: "success" }
            }
        };
    },

    mounted() {
        this.loadData();
    },

    methods: {
        // 加载数据
        async loadData() {
            this.loading = true;
            try {
                const params = {
                    keyword: this.searchForm.keyword || "",
                    status: this.searchForm.status || 0,
                    start_time: this.searchForm.dateRange
                        ? this.searchForm.dateRange[0]
                        : "",
                    end_time: this.searchForm.dateRange
                        ? this.searchForm.dateRange[1]
                        : "",
                    order_column: "create_time",
                    order_by: "desc",
                    page: this.pagination.page,
                    limit: this.pagination.limit
                };

                const res = await this.$request.main.getShippingRecords(params);
                if (res.data && res.data.data) {
                    this.tableData = res.data.data.list || [];
                    this.pagination.total = res.data.data.total || 0;
                } else {
                    this.tableData = [];
                    this.pagination.total = 0;
                }
            } catch (error) {
                this.$message.error("加载数据失败");
                console.error(error);
                this.tableData = [];
                this.pagination.total = 0;
            } finally {
                this.loading = false;
            }
        },

        // 搜索
        handleSearch() {
            this.pagination.page = 1;
            this.loadData();
        },

        // 重置
        handleReset() {
            this.searchForm = {
                keyword: "",
                status: null,
                dateRange: null
            };
            this.pagination.page = 1;
            this.loadData();
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.limit = val;
            this.pagination.page = 1;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.page = val;
            this.loadData();
        },

        // 排序改变
        handleSortChange({ column, prop, order }) {
            // 可以根据需要实现排序逻辑
            console.log("排序改变", { column, prop, order });
        },

        // 工具方法
        maskPhone(phone) {
            if (!phone) return "";
            return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
        },

        getFullAddress(row) {
            const parts = [
                row.province_name,
                row.city_name,
                row.district_name,
                row.address
            ].filter(Boolean);
            return parts.join("");
        },

        getStatusText(status) {
            return this.statusMap[status]?.text || "未知状态";
        },

        getStatusTagType(status) {
            return this.statusMap[status]?.type || "info";
        }
    }
};
</script>

<style scoped>
.shipping-management-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.filter-card {
    margin-bottom: 20px;
}

.search-form {
    margin-bottom: 0;
}

.search-actions {
    text-align: right;
    margin-top: 10px;
}

.table-card {
    margin-bottom: 20px;
}

.product-name {
    font-weight: bold;
    color: #333;
}

.product-code {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

.recipient-name {
    font-weight: bold;
    color: #333;
}

.recipient-phone {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.pagination-wrapper {
    margin-top: 20px;
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .shipping-management-page {
        padding: 10px;
    }

    .search-actions {
        text-align: left;
    }

    .pagination-wrapper {
        text-align: center;
    }
}

/* Element UI 组件样式调整 */
.el-table {
    font-size: 14px;
}

.el-table .el-table__header th {
    background-color: #fafafa;
    color: #333;
    font-weight: bold;
}

.el-tag {
    border-radius: 4px;
}

.el-descriptions {
    margin-top: 10px;
}

.el-form-item {
    margin-bottom: 18px;
}

.el-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-button {
    border-radius: 4px;
}

.el-input,
.el-select {
    border-radius: 4px;
}

.el-dialog {
    border-radius: 8px;
}

.el-dialog__header {
    background-color: #fafafa;
    border-bottom: 1px solid #eee;
}

.el-dialog__title {
    font-weight: bold;
    color: #333;
}
::v-deep .el-form--label-top .el-form-item__label {
    padding: 0;
}
</style>
