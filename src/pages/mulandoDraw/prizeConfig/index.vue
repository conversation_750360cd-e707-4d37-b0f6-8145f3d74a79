<template>
    <div class="prize-config-page">
        <div class="page-header">
            <h1 class="page-title">中奖配置</h1>
            <p class="page-description">
                管理各地理大区的中奖配置，支持独立设置和批量同步
            </p>

            <!-- 批次选择器 -->
            <div class="batch-selector-card">
                <div class="batch-selector-content">
                    <div class="selector-row">
                        <label class="selector-label">选择批次:</label>
                        <el-select
                            v-model="currentBatchId"
                            placeholder="请选择批次"
                            @change="handleBatchChange"
                            :loading="loading"
                            filterable
                            style="width: 300px"
                        >
                            <el-option
                                v-for="batch in enabledBatches"
                                :key="batch.id"
                                :label="batch.title"
                                :value="batch.id"
                            />
                        </el-select>
                        <el-button
                            icon="el-icon-setting"
                            @click="batchManageModalVisible = true"
                            style="margin-left: 10px"
                        >
                            管理批次
                        </el-button>
                        <div
                            v-if="currentBatch"
                            class="batch-description-inline"
                        >
                            <span class="description-label">描述:</span>
                            <span class="description-text">{{
                                currentBatch.desc
                            }}</span>
                        </div>
                    </div>

                    <!-- 汇总数据 -->
                    <div
                        v-if="
                            currentBatchId && configs.length > 0 && summaryData
                        "
                        class="summary-section"
                    >
                        <div class="summary-row">
                            <span class="summary-title">所有大区汇总:</span>
                            <div class="summary-stats">
                                <div class="summary-stat">
                                    <span class="stat-label">总中奖瓶数:</span>
                                    <span class="stat-value primary">
                                        {{
                                            summaryData.total_ct.toLocaleString()
                                        }}
                                    </span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">总已中瓶数:</span>
                                    <span class="stat-value warning">
                                        {{
                                            summaryData.win_ct.toLocaleString()
                                        }}
                                    </span>
                                </div>
                                <div class="summary-stat">
                                    <span class="stat-label">总剩余瓶数:</span>
                                    <span class="stat-value success">
                                        {{
                                            summaryData.remain_ct.toLocaleString()
                                        }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地理大区配置 -->
        <div
            v-if="currentBatchId"
            v-loading="loading"
            element-loading-text="正在加载批次配置..."
        >
            <el-row :gutter="16">
                <el-col
                    v-for="regionConfig in configs"
                    :key="regionConfig.region"
                    :span="12"
                >
                    <el-card class="region-card" shadow="hover">
                        <div slot="header" class="region-header">
                            <div class="region-title">
                                <h4>{{ regionConfig.region }}</h4>
                                <div class="region-stats">
                                    <div class="stat-item">
                                        <span class="stat-label"
                                            >配置中奖瓶数:</span
                                        >
                                        <span class="stat-value primary">{{
                                            regionConfig.total_ct || 0
                                        }}</span>
                                        <el-button
                                            type="text"
                                            size="mini"
                                            icon="el-icon-edit"
                                            @click="
                                                handleEditTotalBottles(
                                                    regionConfig.region
                                                )
                                            "
                                            class="edit-btn"
                                            title="编辑总中奖瓶数"
                                        />
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label"
                                            >总已中瓶数:</span
                                        >
                                        <span class="stat-value warning">{{
                                            regionConfig.win_ct || 0
                                        }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="region-actions">
                                <el-button
                                    type="primary"
                                    size="mini"
                                    icon="el-icon-plus"
                                    @click="handleAddPrize(regionConfig.region)"
                                >
                                    添加奖品
                                </el-button>
                            </div>
                        </div>

                        <div class="region-content">
                            <!-- 总概率警告 -->
                            <div
                                v-if="getTotalProbability(regionConfig) > 100"
                                class="probability-warning"
                            >
                                <span class="warning-icon">⚠️</span>
                                <span class="warning-text"
                                    >总中奖率超过100%</span
                                >
                                <br />
                                <span class="warning-subtext"
                                    >请调整奖品配置</span
                                >
                            </div>

                            <div class="prize-list">
                                <div class="prize-list-header">
                                    <span class="header-label"
                                        >奖品配置列表</span
                                    >
                                    <div class="bottle-summary">
                                        <span class="summary-label"
                                            >瓶数汇总:</span
                                        >
                                        <span class="summary-value">{{
                                            (
                                                regionConfig.redeem_ct || 0
                                            ).toFixed(2)
                                        }}</span>
                                    </div>
                                </div>
                                <div class="prize-table-wrapper">
                                    <el-table
                                        :data="regionConfig.prizes"
                                        size="mini"
                                        border
                                        :show-header="true"
                                        empty-text="暂无奖品配置，点击右上角添加奖品按钮开始配置"
                                    >
                                        <el-table-column
                                            prop="name"
                                            label="奖品名称"
                                            min-width="140"
                                        >
                                            <template slot-scope="scope">
                                                <div class="prize-name-cell">
                                                    <span class="prize-name">{{
                                                        scope.row.name
                                                    }}</span>
                                                    <div class="prize-tags">
                                                        <el-tag
                                                            :type="
                                                                scope.row
                                                                    .status ===
                                                                'disabled'
                                                                    ? 'danger'
                                                                    : 'success'
                                                            "
                                                            size="mini"
                                                        >
                                                            {{
                                                                scope.row
                                                                    .status ===
                                                                "disabled"
                                                                    ? "禁用"
                                                                    : "启用"
                                                            }}
                                                        </el-tag>
                                                        <el-tag
                                                            type="info"
                                                            size="mini"
                                                        >
                                                            占比:
                                                            {{
                                                                calculatePercentage(
                                                                    scope.row,
                                                                    regionConfig
                                                                )
                                                            }}%
                                                        </el-tag>
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="配置"
                                            min-width="200"
                                            align="center"
                                        >
                                            <template slot-scope="scope">
                                                <div class="config-cell">
                                                    <div class="config-row">
                                                        <span
                                                            class="config-label"
                                                            >积分:</span
                                                        >
                                                        <span
                                                            class="config-value points"
                                                            >{{
                                                                scope.row.points
                                                                    ? scope.row.points.toFixed(
                                                                          1
                                                                      )
                                                                    : "-"
                                                            }}</span
                                                        >
                                                    </div>
                                                    <div class="config-row">
                                                        <span
                                                            class="config-label"
                                                            >中奖瓶数:</span
                                                        >
                                                        <span
                                                            class="config-value bottles"
                                                            >{{
                                                                scope.row
                                                                    .winningBottles
                                                            }}</span
                                                        >
                                                    </div>
                                                    <div class="config-row">
                                                        <span
                                                            class="config-label"
                                                            >中奖次数:</span
                                                        >
                                                        <span
                                                            class="config-value times"
                                                            >{{
                                                                scope.row
                                                                    .availableBottles ||
                                                                    0
                                                            }}</span
                                                        >
                                                    </div>
                                                    <div class="config-row">
                                                        <span
                                                            class="config-label"
                                                            >待兑/已兑(瓶):</span
                                                        >
                                                        <span
                                                            class="config-value status"
                                                            >{{
                                                                (scope.row
                                                                    .availableBottles ||
                                                                    0) -
                                                                    (scope.row
                                                                        .usedBottles ||
                                                                        0)
                                                            }}/{{
                                                                scope.row
                                                                    .usedBottles ||
                                                                    0
                                                            }}</span
                                                        >
                                                    </div>
                                                    <div class="config-row">
                                                        <span
                                                            class="config-label"
                                                            >有效期:</span
                                                        >
                                                        <span
                                                            class="config-value validity"
                                                            >{{
                                                                scope.row
                                                                    .validityDays
                                                            }}天</span
                                                        >
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="操作"
                                            width="120"
                                            align="center"
                                        >
                                            <template slot-scope="scope">
                                                <div class="action-buttons">
                                                    <el-button
                                                        type="text"
                                                        size="mini"
                                                        icon="el-icon-edit"
                                                        @click="
                                                            handleEditPrize(
                                                                regionConfig.region,
                                                                scope.row
                                                            )
                                                        "
                                                        class="edit-action"
                                                        title="编辑奖品"
                                                    />
                                                    <el-popconfirm
                                                        :title="
                                                            scope.row.status ===
                                                            'enabled'
                                                                ? '确定禁用这个奖品吗？'
                                                                : '确定启用这个奖品吗？'
                                                        "
                                                        @confirm="
                                                            handleTogglePrizeStatus(
                                                                scope.row
                                                            )
                                                        "
                                                    >
                                                        <el-button
                                                            slot="reference"
                                                            type="text"
                                                            size="mini"
                                                            :class="
                                                                scope.row
                                                                    .status ===
                                                                'enabled'
                                                                    ? 'disable-action'
                                                                    : 'enable-action'
                                                            "
                                                            :title="
                                                                scope.row
                                                                    .status ===
                                                                'enabled'
                                                                    ? '禁用奖品'
                                                                    : '启用奖品'
                                                            "
                                                        >
                                                            {{
                                                                scope.row
                                                                    .status ===
                                                                "enabled"
                                                                    ? "禁用"
                                                                    : "启用"
                                                            }}
                                                        </el-button>
                                                    </el-popconfirm>
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 缺省页面 - 未选择批次时显示 -->
        <div v-if="!currentBatchId && !loading" class="empty-state">
            <div class="empty-content">
                <div class="empty-icon">
                    <i class="el-icon-box"></i>
                </div>
                <h3 class="empty-title">请选择批次</h3>
                <p class="empty-description">
                    请在上方选择一个批次来查看和管理中奖配置
                </p>
                <div class="empty-actions">
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="batchManageModalVisible = true"
                    >
                        管理批次
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 添加奖品对话框 -->
        <el-dialog
            title="添加奖品"
            :visible.sync="addModalVisible"
            width="500px"
            @close="handleAddModalClose"
        >
            <el-form
                :model="prizeForm"
                :rules="prizeRules"
                ref="prizeForm"
                label-width="120px"
                @submit.native.prevent="handleConfirmAddPrize"
            >
                <el-form-item label="奖品名称" prop="name">
                    <el-input
                        v-model="prizeForm.name"
                        placeholder="请输入奖品名称"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="状态" prop="status">
                    <el-select
                        v-model="prizeForm.status"
                        placeholder="请选择状态"
                        style="width: 100%"
                    >
                        <el-option label="启用" value="enabled" />
                        <el-option label="禁用" value="disabled" />
                    </el-select>
                </el-form-item>

                <el-form-item label="积分" prop="points">
                    <el-input-number
                        v-model="prizeForm.points"
                        :min="0"
                        :max="1"
                        :controls="false"
                        :step="0.01"
                        :precision="2"
                        placeholder="请输入积分(0-1之间)"
                        style="width: 100%"
                        @change="handlePrizeFormChange('points', $event)"
                    />
                </el-form-item>

                <el-form-item label="中奖瓶数" prop="winningBottles">
                    <el-input-number
                        v-model="prizeForm.winningBottles"
                        :min="0"
                        :step="1"
                        :controls="false"
                        :precision="0"
                        :disabled="!prizeForm.points"
                        placeholder="请输入中奖瓶数"
                        style="width: 100%"
                        @change="
                            handlePrizeFormChange('winningBottles', $event)
                        "
                    />
                </el-form-item>

                <el-form-item label="中奖次数" prop="availableBottles">
                    <el-input-number
                        :controls="false"
                        v-model="prizeForm.availableBottles"
                        :min="0"
                        :step="1"
                        :precision="0"
                        :disabled="!prizeForm.points"
                        placeholder="请输入中奖次数"
                        style="width: 100%"
                        @change="
                            handlePrizeFormChange('availableBottles', $event)
                        "
                    />
                </el-form-item>

                <el-form-item label="有效期天数" prop="validityDays">
                    <el-input-number
                        v-model="prizeForm.validityDays"
                        :min="1"
                        :step="1"
                        :controls="false"
                        :precision="0"
                        placeholder="请输入有效期天数"
                        style="width: 100%"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleAddModalCancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="handleConfirmAddPrize"
                    :loading="addLoading"
                    >添加</el-button
                >
            </div>
        </el-dialog>

        <!-- 编辑奖品对话框 -->
        <el-dialog
            title="编辑奖品"
            :visible.sync="editModalVisible"
            width="600px"
            @close="handleEditModalClose"
        >
            <el-form
                :model="editPrizeForm"
                :rules="prizeRules"
                ref="editPrizeForm"
                label-width="120px"
                @submit.native.prevent="handleConfirmEditPrize"
            >
                <el-form-item label="奖品名称" prop="name">
                    <el-input
                        v-model="editPrizeForm.name"
                        placeholder="请输入奖品名称"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="状态" prop="status">
                    <el-select
                        v-model="editPrizeForm.status"
                        placeholder="请选择状态"
                        style="width: 100%"
                    >
                        <el-option label="启用" value="enabled" />
                        <el-option label="禁用" value="disabled" />
                    </el-select>
                </el-form-item>

                <el-form-item label="积分" prop="points">
                    <el-input-number
                        v-model="editPrizeForm.points"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        :precision="2"
                        placeholder="请输入积分(0-1之间)"
                        style="width: 100%"
                        @change="handleEditPrizeFormChange('points', $event)"
                    />
                </el-form-item>

                <el-form-item label="中奖瓶数" prop="winningBottles">
                    <el-input-number
                        v-model="editPrizeForm.winningBottles"
                        :min="0"
                        :step="1"
                        :precision="0"
                        :disabled="!editPrizeForm.points"
                        placeholder="请输入中奖瓶数"
                        style="width: 100%"
                        @change="
                            handleEditPrizeFormChange('winningBottles', $event)
                        "
                    />
                </el-form-item>

                <el-form-item label="中奖次数" prop="availableBottles">
                    <el-input-number
                        v-model="editPrizeForm.availableBottles"
                        :min="1"
                        :step="1"
                        :precision="0"
                        :disabled="!editPrizeForm.points"
                        placeholder="请输入中奖次数"
                        style="width: 100%"
                        @change="
                            handleEditPrizeFormChange(
                                'availableBottles',
                                $event
                            )
                        "
                    />
                </el-form-item>

                <el-form-item label="已中次数">
                    <el-input-number
                        v-model="editPrizeForm.usedBottles"
                        :step="1"
                        :precision="0"
                        placeholder="已中次数"
                        disabled
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="有效期天数" prop="validityDays">
                    <el-input-number
                        v-model="editPrizeForm.validityDays"
                        :min="1"
                        :step="1"
                        :precision="0"
                        placeholder="请输入有效期天数"
                        style="width: 100%"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleEditModalCancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="handleConfirmEditPrize"
                    :loading="editLoading"
                    >保存</el-button
                >
            </div>
        </el-dialog>

        <!-- 批次管理模态框 -->
        <el-dialog
            title="批次管理"
            :visible.sync="batchManageModalVisible"
            width="1000px"
        >
            <div class="batch-header" style="margin-bottom: 16px;">
                <div class="batch-filters">
                    <el-select
                        v-model="batchStatusFilter"
                        placeholder="选择状态"
                        @change="loadBatchConfigs"
                        style="width: 120px; margin-right: 10px;"
                    >
                        <el-option label="全部" :value="0" />
                        <el-option label="启用" :value="1" />
                        <el-option label="禁用" :value="2" />
                    </el-select>
                    <el-button
                        type="default"
                        size="small"
                        icon="el-icon-refresh"
                        @click="loadBatchConfigs"
                        style="margin-right: 10px;"
                    >
                        刷新
                    </el-button>
                    <el-button
                        type="primary"
                        size="small"
                        icon="el-icon-plus"
                        @click="handleAddBatch"
                    >
                        添加批次
                    </el-button>
                </div>
            </div>
            <el-table
                :data="batchList"
                size="small"
                border
                v-loading="batchListLoading"
            >
                <el-table-column
                    prop="title"
                    label="批次标题"
                    min-width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="80"
                    align="center"
                />
                <el-table-column
                    prop="goods_name"
                    label="商品名称"
                    min-width="140"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="desc"
                    label="描述"
                    width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="status"
                    label="状态"
                    width="80"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            :type="
                                scope.row.status === 1 ? 'success' : 'danger'
                            "
                            size="mini"
                        >
                            {{ scope.row.status === 1 ? "启用" : "禁用" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="时间" width="160">
                    <template slot-scope="scope">
                        <div class="time-info">
                            <div class="time-row">
                                <span class="time-label">开始:</span>
                                <span class="time-value">{{
                                    formatDate(scope.row.start_time)
                                }}</span>
                            </div>
                            <div class="time-row">
                                <span class="time-label">结束:</span>
                                <span class="time-value">{{
                                    formatDate(scope.row.end_time)
                                }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="120"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            icon="el-icon-edit"
                            @click="handleEditBatch(scope.row)"
                            title="编辑"
                        />
                        <el-button
                            type="text"
                            size="mini"
                            :class="
                                scope.row.status === 1
                                    ? 'toggle-disable'
                                    : 'toggle-enable'
                            "
                            @click="
                                handleToggleBatchStatus(
                                    scope.row.id,
                                    scope.row.status === 2
                                )
                            "
                            :title="scope.row.status === 1 ? '禁用' : '启用'"
                        >
                            {{ scope.row.status === 1 ? "禁用" : "启用" }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div style="margin-top: 16px; text-align: right;">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="batchPagination.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="batchPagination.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="batchPagination.total"
                    small
                />
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchManageModalVisible = false"
                    >关闭</el-button
                >
            </div>
        </el-dialog>

        <!-- 批次表单模态框 -->
        <el-dialog
            :title="editingBatch ? '编辑批次' : '添加批次'"
            :visible.sync="batchFormModalVisible"
            width="500px"
            @close="handleBatchFormModalClose"
        >
            <el-form
                :model="batchForm"
                :rules="batchRules"
                ref="batchForm"
                label-width="100px"
            >
                <el-form-item label="批次标题" prop="title">
                    <el-input
                        v-model="batchForm.title"
                        placeholder="请输入批次标题"
                        maxlength="50"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item label="简码" prop="short_code">
                    <el-input
                        v-model="batchForm.short_code"
                        placeholder="请输入简码，如：m218"
                    />
                </el-form-item>
                <el-form-item label="商品名称" prop="goods_name">
                    <el-input
                        v-model="batchForm.goods_name"
                        placeholder="请输入商品名称"
                        maxlength="100"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item label="描述" prop="desc">
                    <el-input
                        type="textarea"
                        v-model="batchForm.desc"
                        placeholder="请输入批次描述（可选）"
                        :rows="3"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item label="开始时间" prop="start_time">
                    <el-date-picker
                        v-model="batchForm.start_time"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="结束时间" prop="end_time">
                    <el-date-picker
                        v-model="batchForm.end_time"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select
                        v-model="batchForm.status"
                        placeholder="请选择批次状态"
                        style="width: 100%"
                    >
                        <el-option label="启用" :value="1" />
                        <el-option label="禁用" :value="2" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchFormModalVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="handleBatchFormSubmit"
                    :loading="batchFormLoading"
                >
                    {{ editingBatch ? "更新" : "添加" }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 总中奖瓶数编辑模态框 -->
        <el-dialog
            :title="`编辑 ${editingRegionForBottles} 总中奖瓶数`"
            :visible.sync="totalBottlesEditModalVisible"
            width="400px"
            @close="handleTotalBottlesModalClose"
        >
            <el-form
                :model="totalBottlesForm"
                :rules="totalBottlesRules"
                ref="totalBottlesForm"
                label-width="120px"
            >
                <el-form-item label="总中奖瓶数" prop="totalBottles">
                    <el-input-number
                        v-model="totalBottlesForm.totalBottles"
                        :min="0"
                        :step="1"
                        :precision="0"
                        placeholder="请输入总中奖瓶数"
                        style="width: 100%"
                    />
                </el-form-item>
                <div class="tips-section">
                    <p class="tip-title">💡 提示：</p>
                    <p class="tip-text">
                        • 总中奖瓶数是该地区所有奖品的中奖次数总和
                    </p>
                    <p class="tip-text">• 修改后将更新该地区的汇总统计数据</p>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="totalBottlesEditModalVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="handleSaveTotalBottles"
                    :loading="totalBottlesLoading"
                >
                    保存
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "PrizeConfig",
    data() {
        return {
            loading: false,
            addLoading: false,
            editLoading: false,
            batchFormLoading: false,
            totalBottlesLoading: false,

            // 批次管理状态
            currentBatchId: "",
            batchList: [],
            batchListLoading: false,
            batchStatusFilter: 0,
            batchPagination: {
                page: 1,
                limit: 10,
                total: 0
            },

            // API返回的汇总数据
            summaryData: null,

            // 奖品管理状态
            addModalVisible: false,
            editModalVisible: false,
            editingPrize: null,
            currentRegion: "", // 当前操作的地理大区

            // 批次管理状态
            batchManageModalVisible: false,
            batchFormModalVisible: false,
            editingBatch: null,

            // 总中奖瓶数编辑状态
            totalBottlesEditModalVisible: false,
            editingRegionForBottles: "",

            // 奖品表单
            prizeForm: {
                name: "",
                points: 0,
                winningBottles: 0,
                availableBottles: 0,
                validityDays: 30,
                status: "enabled"
            },

            // 编辑奖品表单
            editPrizeForm: {
                id: "",
                name: "",
                points: 0,
                winningBottles: 0,
                availableBottles: 0,
                usedBottles: 0,
                validityDays: 30,
                status: "enabled"
            },

            // 批次表单
            batchForm: {
                title: "",
                short_code: "",
                goods_name: "",
                desc: "",
                start_time: "",
                end_time: "",
                status: 1
            },

            // 总中奖瓶数表单
            totalBottlesForm: {
                totalBottles: 0
            },

            // 表单验证规则
            prizeRules: {
                name: [
                    {
                        required: true,
                        message: "请输入奖品名称",
                        trigger: "change"
                    }
                ],
                status: [
                    { required: true, message: "请选择状态", trigger: "change" }
                ],
                points: [
                    { required: true, message: "请输入积分", trigger: "blur" },
                    {
                        type: "number",
                        min: 0,
                        max: 1,
                        message: "积分必须在0到1之间",
                        trigger: "blur"
                    }
                ],
                winningBottles: [
                    {
                        required: true,
                        message: "请输入中奖瓶数",
                        trigger: "blur"
                    },
                    {
                        type: "number",
                        min: 0,
                        message: "中奖瓶数不能小于0",
                        trigger: "blur"
                    }
                ],
                availableBottles: [
                    {
                        required: true,
                        message: "请输入中奖次数",
                        trigger: "blur"
                    },
                    {
                        type: "number",
                        min: 1,
                        message: "中奖次数必须大于0",
                        trigger: "blur"
                    }
                ],
                validityDays: [
                    {
                        required: true,
                        message: "请输入有效期天数",
                        trigger: "blur"
                    },
                    {
                        type: "number",
                        min: 1,
                        message: "有效期天数必须大于0",
                        trigger: "blur"
                    }
                ]
            },

            // 批次表单验证规则
            batchRules: {
                title: [
                    {
                        required: true,
                        message: "请输入批次标题",
                        trigger: "blur"
                    },
                    {
                        max: 50,
                        message: "批次标题不能超过50个字符",
                        trigger: "blur"
                    }
                ],
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur"
                    }
                ],
                goods_name: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur"
                    },
                    {
                        max: 100,
                        message: "商品名称不能超过100个字符",
                        trigger: "blur"
                    }
                ],
                desc: [
                    {
                        max: 200,
                        message: "描述不能超过200个字符",
                        trigger: "blur"
                    }
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择开始时间",
                        trigger: "change"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择结束时间",
                        trigger: "change"
                    }
                ],
                status: [
                    {
                        required: true,
                        message: "请选择批次状态",
                        trigger: "change"
                    }
                ]
            },

            // 总中奖瓶数验证规则
            totalBottlesRules: {
                totalBottles: [
                    {
                        required: true,
                        message: "请输入总中奖瓶数",
                        trigger: "blur"
                    },
                    {
                        type: "number",
                        min: 0,
                        message: "总中奖瓶数不能小于0",
                        trigger: "blur"
                    }
                ]
            },

            // 奖品类型
            prizeTypes: [
                "再来一瓶",
                "5折购酒",
                "7折购酒",
                "9折购酒",
                "免费试饮",
                "精美礼品",
                "现金红包",
                "积分奖励"
            ],

            // 地理大区列表
            regions: [
                "华北地区",
                "华东地区",
                "华中地区",
                "华南地区",
                "西南地区",
                "西北地区",
                "东北地区"
            ]
        };
    },

    computed: {
        // 当前批次配置
        currentBatch() {
            return this.batchList.find(
                batch => batch.id === this.currentBatchId
            );
        },

        // 当前批次的地理大区配置
        configs() {
            return this.currentBatch?.regionConfigs || [];
        },

        // 启用状态的批次列表
        enabledBatches() {
            return this.batchList.filter(batch => batch.status === 1);
        }
    },

    mounted() {
        this.loadBatchConfigs();
    },

    watch: {
        // 保留其他必要的监听器，移除自动计算相关的监听器
        // 自动计算功能现在通过方法调用实现
    },

    methods: {
        // 加载批次配置
        async loadBatchConfigs() {
            this.batchListLoading = true;
            try {
                // 保存当前选中批次的regionConfigs数据
                let currentBatchRegionConfigs = null;
                if (this.currentBatchId) {
                    const currentBatch = this.batchList.find(
                        b => b.id == this.currentBatchId
                    );
                    if (currentBatch && currentBatch.regionConfigs) {
                        currentBatchRegionConfigs = currentBatch.regionConfigs;
                    }
                }

                const params = {
                    status: this.batchStatusFilter,
                    page: this.batchPagination.page,
                    limit: this.batchPagination.limit
                };
                const res = await this.$request.main.getBatchConfigs(params);
                if (res.data && res.data.data) {
                    this.batchList = res.data.data.list || [];
                    this.batchPagination.total = res.data.data.total || 0;

                    // 如果之前有选中的批次，恢复其regionConfigs数据
                    if (this.currentBatchId && currentBatchRegionConfigs) {
                        const updatedCurrentBatch = this.batchList.find(
                            b => b.id == this.currentBatchId
                        );
                        if (updatedCurrentBatch) {
                            this.$set(
                                updatedCurrentBatch,
                                "regionConfigs",
                                currentBatchRegionConfigs
                            );
                        }
                    }

                    // 不自动选择批次，让用户手动选择
                    // 这样可以显示缺省页面，提示用户选择批次
                }
            } catch (error) {
                this.$message.error("加载批次配置失败");
                console.error(error);
            } finally {
                this.batchListLoading = false;
            }
        },

        // 批次切换处理
        async handleBatchChange(batchId) {
            // 如果有打开的模态框，先关闭并清理状态
            if (this.addModalVisible || this.editModalVisible) {
                this.addModalVisible = false;
                this.editModalVisible = false;
                this.editingPrize = null;
                this.currentRegion = "";
                this.$refs.prizeForm?.resetFields();
                this.$refs.editPrizeForm?.resetFields();
            }

            this.loading = true;
            try {
                // 设置当前批次ID
                this.currentBatchId = batchId;

                // 清空当前配置数据，使用 $set 确保响应式更新
                const currentBatch = this.batchList.find(b => b.id === batchId);
                if (currentBatch) {
                    this.$set(currentBatch, "regionConfigs", []);
                }

                // 加载批次的奖品配置详情
                console.log("777");
                await this.loadPrizeConfigDetail(batchId);
            } catch (error) {
                this.$message.error("切换批次失败");
                console.error(error);
            } finally {
                this.loading = false;
            }
        },

        // 加载批次奖品配置详情
        async loadPrizeConfigDetail(batchId) {
            try {
                const res = await this.$request.main.getPrizeConfDetail({
                    id: batchId
                });

                if (res.data && res.data.data) {
                    const data = res.data.data;
                    // 将API返回的数据转换为组件需要的格式
                    this.convertApiDataToRegionConfigs(data, batchId);
                }
            } catch (error) {
                console.error("加载奖品配置详情失败:", error);
                // 如果加载失败，使用空的地区配置
            }
        },

        // 将API数据转换为地区配置格式
        convertApiDataToRegionConfigs(apiData, batchId) {
            // 保存API返回的汇总数据
            this.summaryData = {
                total_ct: apiData.total_ct || 0,
                win_ct: apiData.win_ct || 0,
                remain_ct: apiData.remain_ct || 0
            };

            // 根据API返回的数据结构转换
            const regionConfigs = [];

            if (
                apiData.region_conf_list &&
                Array.isArray(apiData.region_conf_list)
            ) {
                apiData.region_conf_list.forEach(regionData => {
                    const regionConfig = {
                        region: regionData.region_name,
                        regionCode: regionData.region_code,
                        prizes: [],
                        totalProbability: 0,
                        // 使用API返回的数据
                        total_ct: regionData.total_ct || 0,
                        redeem_ct: regionData.redeem_ct || 0,
                        win_ct: regionData.win_ct || 0,
                        remain_ct: regionData.remain_ct || 0,
                        // 保持兼容性的字段
                        totalAvailableBottles: regionData.redeem_ct || 0,
                        totalUsedBottles: regionData.win_ct || 0,
                        updatedAt: new Date().toLocaleString()
                    };

                    // 转换奖品列表
                    if (
                        regionData.prize_list &&
                        Array.isArray(regionData.prize_list)
                    ) {
                        regionConfig.prizes = regionData.prize_list.map(
                            prizeData => ({
                                id: prizeData.id.toString(),
                                name: prizeData.prize_name,
                                points: prizeData.points / 100, // API返回的是分，转换为0-1之间的小数
                                winningBottles: prizeData.redeem_quantity || 0,
                                availableBottles: prizeData.prize_ct || 0,
                                usedBottles: prizeData.win_ct || 0,
                                validityDays: prizeData.valid_days || 30,
                                status:
                                    prizeData.status === 1
                                        ? "enabled"
                                        : "disabled",
                                regionCode: prizeData.region_code,
                                batchId: prizeData.batch_id
                            })
                        );
                    }

                    regionConfigs.push(regionConfig);
                });
            }

            // 更新当前批次的地区配置
            const currentBatch = this.batchList.find(b => b.id === batchId);

            if (currentBatch) {
                // 使用 Vue.set 确保响应式更新
                this.$set(currentBatch, "regionConfigs", regionConfigs);
            }
        },

        // 分页处理方法
        handleSizeChange(val) {
            this.batchPagination.limit = val;
            this.batchPagination.page = 1;
            this.loadBatchConfigs();
        },

        handleCurrentChange(val) {
            this.batchPagination.page = val;
            this.loadBatchConfigs();
        },

        // 批次管理相关方法
        handleAddBatch() {
            this.editingBatch = null;
            this.batchForm = {
                title: "",
                short_code: "",
                goods_name: "",
                desc: "",
                start_time: "",
                end_time: "",
                status: 1
            };
            this.batchFormModalVisible = true;
        },

        handleEditBatch(batch) {
            this.editingBatch = batch;
            this.batchForm = {
                title: batch.title,
                short_code: batch.short_code,
                goods_name: batch.goods_name,
                desc: batch.desc,
                start_time: batch.start_time,
                end_time: batch.end_time,
                status: batch.status
            };
            this.batchFormModalVisible = true;
        },

        async handleDeleteBatch(batchId) {
            if (batchId === this.currentBatchId) {
                this.$message.error("无法删除当前正在使用的批次");
                return;
            }

            // 注意：当前API中没有提供删除批次的接口
            // 这里暂时提示用户功能未实现
            console.log(`尝试删除批次 ID: ${batchId}`);
            this.$message.warning("删除批次功能暂未实现，请联系管理员");
        },

        async handleToggleBatchStatus(batchId, isEnable) {
            try {
                const data = {
                    id: batchId,
                    is_enable: isEnable
                };
                await this.$request.main.batchChange(data);
                this.$message.success(`批次已${isEnable ? "启用" : "禁用"}`);

                // 重新加载批次列表
                await this.loadBatchConfigs();

                // 只有当操作的批次是当前选中的批次时，才重新加载其详细配置
                // 避免影响其他批次的数据显示
            } catch (error) {
                console.error(error);
                this.$message.error("操作失败");
            }
        },

        // 批次表单提交处理
        async handleBatchFormSubmit() {
            try {
                await this.$refs.batchForm.validate();

                // 验证时间范围
                if (this.batchForm.start_time && this.batchForm.end_time) {
                    if (
                        new Date(this.batchForm.start_time) >=
                        new Date(this.batchForm.end_time)
                    ) {
                        this.$message.error("结束时间必须晚于开始时间");
                        return;
                    }
                }

                this.batchFormLoading = true;

                if (this.editingBatch) {
                    // 编辑批次 - 使用updateBatch API
                    const data = {
                        id: this.editingBatch.id,
                        title: this.batchForm.title,
                        short_code: this.batchForm.short_code,
                        goods_name: this.batchForm.goods_name,
                        desc: this.batchForm.desc,
                        start_time: this.batchForm.start_time,
                        end_time: this.batchForm.end_time,
                        status: this.batchForm.status
                    };

                    const res = await this.$request.main.updateBatch(data);

                    if (res.data && res.data.error_code === 0) {
                        this.$message.success("批次更新成功");

                        // 重新加载批次列表
                        await this.loadBatchConfigs();

                        // 如果当前选中的批次被编辑了，需要重新加载其详细配置
                    } else {
                        this.$message.error(res.data?.msg || "批次更新失败");
                        return;
                    }
                } else {
                    // 添加新批次
                    const data = {
                        title: this.batchForm.title,
                        short_code: this.batchForm.short_code,
                        goods_name: this.batchForm.goods_name,
                        desc: this.batchForm.desc,
                        start_time: this.batchForm.start_time,
                        end_time: this.batchForm.end_time,
                        status: this.batchForm.status
                    };

                    const res = await this.$request.main.createBatch(data);

                    if (res.data && res.data.error_code === 0) {
                        this.$message.success("批次添加成功");
                        // 重新加载批次列表
                        this.loadBatchConfigs();
                    } else {
                        this.$message.error(res.data?.msg || "批次添加失败");
                        return;
                    }
                }

                this.batchFormModalVisible = false;
                this.editingBatch = null;
                this.$refs.batchForm.resetFields();
            } catch (error) {
                console.error("批次表单提交失败:", error);
                if (error.response?.data?.msg) {
                    this.$message.error(error.response.data.msg);
                } else if (error.message) {
                    this.$message.error(error.message);
                } else {
                    this.$message.error(
                        this.editingBatch ? "批次更新失败" : "批次添加失败"
                    );
                }
            } finally {
                this.batchFormLoading = false;
            }
        },

        handleBatchFormModalClose() {
            this.$refs.batchForm?.resetFields();
            this.editingBatch = null;
        },

        // 格式化日期
        formatDate(dateStr) {
            if (!dateStr) return "-";
            return dateStr.split(" ")[0]; // 只显示日期部分
        },

        // 添加奖品
        handleAddPrize(region) {
            this.currentRegion = region;
            this.prizeForm = {
                name: "",
                points: 0,
                winningBottles: 0,
                availableBottles: 0,
                validityDays: 30,
                status: "enabled"
            };
            this.addModalVisible = true;
        },

        // 确认添加奖品
        async handleConfirmAddPrize() {
            try {
                await this.$refs.prizeForm.validate();

                this.addLoading = true;

                // 获取当前地区的regionCode
                const currentRegionConfig = this.configs.find(
                    config => config.region === this.currentRegion
                );
                if (!currentRegionConfig) {
                    this.$message.error("无法找到当前地区配置");
                    return;
                }

                // 准备API请求数据
                const apiData = {
                    batch_id: this.currentBatchId,
                    region_code: currentRegionConfig.regionCode,
                    prize_name: this.prizeForm.name,
                    status: this.prizeForm.status === "enabled" ? 1 : 2,
                    points: Math.round(this.prizeForm.points * 100), // 转换为分(0-1小数*100)
                    redeem_quantity: this.prizeForm.winningBottles,
                    prize_ct: this.prizeForm.availableBottles,
                    valid_days: this.prizeForm.validityDays
                };

                // 调用添加奖品API
                const res = await this.$request.main.addPrize(apiData);

                if (res.data.error_code === 0) {
                    this.$message.success("添加奖品成功");
                    // 重新加载当前批次的配置数据
                    console.log("333");
                    await this.loadPrizeConfigDetail(this.currentBatchId);
                    this.addModalVisible = false;
                    this.currentRegion = "";
                    this.$refs.prizeForm.resetFields();
                } else {
                    this.$message.error(res.data?.msg || "添加奖品失败");
                }
            } catch (error) {
                console.error("添加奖品失败:", error);
                if (error.response?.data?.msg) {
                    this.$message.error(error.response.data.msg);
                } else if (error.message) {
                    this.$message.error(error.message);
                } else {
                    this.$message.error("添加奖品失败");
                }
            } finally {
                this.addLoading = false;
            }
        },

        // 添加对话框关闭
        handleAddModalClose() {
            this.$refs.prizeForm?.resetFields();
        },

        // 编辑奖品
        handleEditPrize(region, prize) {
            this.currentRegion = region;
            this.editingPrize = prize;
            this.editPrizeForm = {
                id: prize.id,
                name: prize.name,
                points: prize.points,
                winningBottles: prize.winningBottles,
                availableBottles: prize.availableBottles,
                usedBottles: prize.usedBottles,
                validityDays: prize.validityDays,
                status: prize.status || "enabled"
            };
            this.editModalVisible = true;
        },

        // 确认编辑奖品
        async handleConfirmEditPrize() {
            try {
                await this.$refs.editPrizeForm.validate();

                this.editLoading = true;

                if (!this.editingPrize) return;

                // 准备API请求数据
                const apiData = {
                    id: parseInt(this.editPrizeForm.id),
                    prize_name: this.editPrizeForm.name,
                    status: this.editPrizeForm.status === "enabled" ? 1 : 2,
                    points: Math.round(this.editPrizeForm.points * 100), // 转换为分(0-1小数*100)
                    redeem_quantity: this.editPrizeForm.winningBottles,
                    prize_ct: this.editPrizeForm.availableBottles,
                    valid_days: this.editPrizeForm.validityDays
                };

                // 调用更新奖品API
                const res = await this.$request.main.updatePrize(apiData);

                if (res.data.error_code === 0) {
                    this.$message.success("奖品修改成功");
                    // 重新加载当前批次的配置数据
                    console.log("444");
                    await this.loadPrizeConfigDetail(this.currentBatchId);
                    this.editModalVisible = false;
                    this.editingPrize = null;
                    this.currentRegion = "";
                    this.$refs.editPrizeForm.resetFields();
                } else {
                    this.$message.error(res.data?.msg || "修改奖品失败");
                }
            } catch (error) {
                console.error("编辑奖品失败:", error);
                if (error.response?.data?.msg) {
                    this.$message.error(error.response.data.msg);
                } else if (error.message) {
                    this.$message.error(error.message);
                } else {
                    this.$message.error("编辑奖品失败");
                }
            } finally {
                this.editLoading = false;
            }
        },

        // 编辑对话框关闭
        handleEditModalClose() {
            this.$refs.editPrizeForm?.resetFields();
        },

        // 切换奖品启用/禁用状态
        async handleTogglePrizeStatus(prize) {
            try {
                // 准备更新数据，只修改status字段，其他数据保持不变
                const updateData = {
                    id: +prize.id,
                    batch_id: prize.batchId,
                    region_code: prize.regionCode,
                    prize_name: prize.name,
                    status: prize.status === "enabled" ? 2 : 1, // 1启用，2禁用
                    points: Math.round(prize.points * 100), // 转换回分
                    redeem_quantity: prize.winningBottles,
                    prize_ct: prize.availableBottles,
                    valid_days: prize.validityDays
                };

                const res = await this.$request.main.updatePrize(updateData);

                if (res.data && res.data.error_code === 0) {
                    const action = prize.status === "enabled" ? "禁用" : "启用";
                    this.$message.success(`奖品${action}成功`);

                    // 立即更新本地状态，确保UI响应
                    const newStatus =
                        prize.status === "enabled" ? "disabled" : "enabled";
                    this.$set(prize, "status", newStatus);

                    // 重新加载配置数据以确保数据同步
                    console.log("555");
                    await this.loadPrizeConfigDetail(this.currentBatchId);
                }
            } catch (error) {
                console.error("切换奖品状态失败:", error);
                this.$message.error("操作失败");
            }
        },

        // 编辑总中奖瓶数
        handleEditTotalBottles(region) {
            const regionConfig = this.configs.find(
                config => config.region === region
            );
            if (regionConfig) {
                // 优先使用手动设置的总中奖瓶数，否则使用计算值
                const totalBottles =
                    regionConfig.totalAvailableBottles !== undefined
                        ? regionConfig.totalAvailableBottles
                        : this.calculateTotalAvailableBottles(regionConfig);

                this.editingRegionForBottles = region;
                this.totalBottlesForm.totalBottles = totalBottles;
                this.totalBottlesEditModalVisible = true;
            }
        },

        // 保存总中奖瓶数
        async handleSaveTotalBottles() {
            try {
                await this.$refs.totalBottlesForm.validate();

                this.totalBottlesLoading = true;

                // 获取当前地区的regionCode
                const currentRegionConfig = this.configs.find(
                    config => config.region === this.editingRegionForBottles
                );
                if (!currentRegionConfig) {
                    this.$message.error("无法找到当前地区配置");
                    return;
                }

                // 准备API请求数据
                const apiData = {
                    batch_id: this.currentBatchId,
                    region_code: currentRegionConfig.regionCode,
                    prize_ct: this.totalBottlesForm.totalBottles
                };

                // 调用大区数量更新API
                const res = await this.$request.main.batchRegionConfUpdare(
                    apiData
                );

                if (res.data.error_code === 0) {
                    this.$message.success("总中奖瓶数更新成功");
                    // 重新加载当前批次的配置数据
                    console.log("666");
                    await this.loadPrizeConfigDetail(this.currentBatchId);
                    this.totalBottlesEditModalVisible = false;
                    this.editingRegionForBottles = "";
                    this.$refs.totalBottlesForm.resetFields();
                }
            } catch (error) {
                console.error("保存总中奖瓶数失败:", error);
                if (error.response?.data?.msg) {
                    this.$message.error(error.response.data.msg);
                } else {
                    this.$message.error("保存失败");
                }
            } finally {
                this.totalBottlesLoading = false;
            }
        },

        handleTotalBottlesModalClose() {
            this.$refs.totalBottlesForm?.resetFields();
            this.editingRegionForBottles = "";
        },

        // 模板中使用的辅助方法
        getTotalProbability(regionConfig) {
            return regionConfig.prizes.reduce(
                (sum, prize) => sum + (prize.probability || 0),
                0
            );
        },

        getTotalAvailableBottles(regionConfig) {
            return regionConfig.totalAvailableBottles !== undefined
                ? regionConfig.totalAvailableBottles
                : this.calculateTotalAvailableBottles(regionConfig);
        },

        getTotalUsedBottles(regionConfig) {
            return regionConfig.prizes.reduce(
                (sum, prize) => sum + (prize.usedBottles || 0),
                0
            );
        },

        getTotalWinningBottles(regionConfig) {
            return regionConfig.prizes.reduce(
                (sum, prize) => sum + (prize.winningBottles || 0),
                0
            );
        },

        calculatePercentage(prize, regionConfig) {
            const totalAvailableBottlesInRegion = regionConfig.prizes.reduce(
                (sum, p) => sum + (p.availableBottles || 0),
                0
            );
            if (totalAvailableBottlesInRegion === 0) return "0.0";
            return (
                ((prize.availableBottles || 0) /
                    totalAvailableBottlesInRegion) *
                100
            ).toFixed(1);
        },

        // 添加奖品表单自动计算方法 - 按照参考文件样式
        handlePrizeFormChange(changedField, newVal) {
            if (newVal === null || newVal === undefined) return;

            // 当积分或中奖瓶数变化时，自动计算另一个值
            if (
                changedField === "points" &&
                this.prizeForm.winningBottles &&
                newVal > 0
            ) {
                const calculatedAvailableBottles = Math.floor(
                    this.prizeForm.winningBottles / newVal
                );
                this.$set(
                    this.prizeForm,
                    "availableBottles",
                    calculatedAvailableBottles
                );
            } else if (
                changedField === "winningBottles" &&
                this.prizeForm.points &&
                newVal >= 0
            ) {
                const calculatedAvailableBottles =
                    this.prizeForm.points > 0
                        ? Math.floor(newVal / this.prizeForm.points)
                        : 0;
                this.$set(
                    this.prizeForm,
                    "availableBottles",
                    calculatedAvailableBottles
                );
            } else if (
                changedField === "availableBottles" &&
                this.prizeForm.points &&
                newVal >= 0
            ) {
                const calculatedWinningBottles = Math.floor(
                    newVal * this.prizeForm.points
                );
                this.$set(
                    this.prizeForm,
                    "winningBottles",
                    calculatedWinningBottles
                );
            }
        },

        // 编辑奖品表单自动计算方法 - 按照参考文件样式
        handleEditPrizeFormChange(changedField, newVal) {
            if (newVal === null || newVal === undefined) return;

            // 当积分或中奖瓶数变化时，自动计算另一个值
            if (
                changedField === "points" &&
                this.editPrizeForm.winningBottles &&
                newVal > 0
            ) {
                const calculatedAvailableBottles = Math.floor(
                    this.editPrizeForm.winningBottles / newVal
                );
                this.$set(
                    this.editPrizeForm,
                    "availableBottles",
                    calculatedAvailableBottles
                );
            } else if (
                changedField === "winningBottles" &&
                this.editPrizeForm.points &&
                newVal >= 0
            ) {
                const calculatedAvailableBottles =
                    this.editPrizeForm.points > 0
                        ? Math.floor(newVal / this.editPrizeForm.points)
                        : 0;
                this.$set(
                    this.editPrizeForm,
                    "availableBottles",
                    calculatedAvailableBottles
                );
            } else if (
                changedField === "availableBottles" &&
                this.editPrizeForm.points &&
                newVal >= 0
            ) {
                const calculatedWinningBottles = Math.floor(
                    newVal * this.editPrizeForm.points
                );
                this.$set(
                    this.editPrizeForm,
                    "winningBottles",
                    calculatedWinningBottles
                );
            }
        },

        // 添加奖品对话框取消处理
        handleAddModalCancel() {
            this.addModalVisible = false;
            this.currentRegion = "";
            this.$refs.prizeForm?.resetFields();
        },

        // 编辑奖品对话框取消处理
        handleEditModalCancel() {
            this.editModalVisible = false;
            this.editingPrize = null;
            this.currentRegion = "";
            this.$refs.editPrizeForm?.resetFields();
        }
    }
};
</script>

<style scoped>
.prize-config-page {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.page-header {
    margin-bottom: 32px;
}

.page-title {
    font-size: 32px;
    font-weight: bold;
    color: #1f2937;
    margin: 0 0 12px 0;
}

.page-description {
    font-size: 18px;
    color: #6b7280;
    margin: 0 0 24px 0;
}

.batch-selector-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.batch-selector-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.selector-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.selector-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

.batch-description-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
}

.description-label {
    font-weight: 500;
}

.description-text {
    color: #374151;
}

.summary-section {
    border-top: 1px solid #f3f4f6;
    padding-top: 16px;
}

.summary-row {
    display: flex;
    align-items: center;
    gap: 32px;
    flex-wrap: wrap;
}

.summary-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.summary-stats {
    display: flex;
    align-items: center;
    gap: 32px;
    flex-wrap: wrap;
}

.summary-stat {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

.stat-value {
    font-weight: bold;
    font-size: 18px;
}

.stat-value.primary {
    color: #3b82f6;
}

.stat-value.warning {
    color: #f59e0b;
}

.stat-value.success {
    color: #10b981;
}

/* 地理大区卡片样式 */
.region-card {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s;
}

.region-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.region-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.region-title h4 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: bold;
}

.region-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.edit-btn {
    padding: 2px 4px;
    color: #3b82f6;
}

.edit-btn:hover {
    background-color: #eff6ff;
}

.region-actions {
    flex-shrink: 0;
}

.probability-warning {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    color: #dc2626;
    font-size: 14px;
}

.warning-icon {
    font-weight: bold;
}

.warning-text {
    font-weight: 500;
}

.warning-subtext {
    font-size: 12px;
}

.prize-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.header-label {
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
}

.bottle-summary {
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-label {
    font-size: 12px;
    color: #6b7280;
}

.summary-value {
    font-weight: bold;
    font-size: 14px;
    color: #10b981;
}

.prize-table-wrapper {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
}

.prize-name-cell {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.prize-name {
    font-weight: 500;
    color: #1f2937;
}

.prize-tags {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
}

.prize-tags .el-tag {
    width: auto;
    display: inline-block;
}

.config-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-label {
    font-size: 12px;
    color: #6b7280;
}

.config-value {
    font-weight: 600;
    font-size: 12px;
}

.config-value.points {
    color: #9333ea;
}

.config-value.bottles {
    color: #059669;
}

.config-value.times {
    color: #2563eb;
}

.config-value.status {
    color: #d97706;
}

.config-value.validity {
    color: #6b7280;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.edit-action {
    color: #3b82f6;
    padding: 4px 8px;
}

.edit-action:hover {
    background-color: #eff6ff;
}

.disable-action {
    color: #dc2626;
    padding: 4px 8px;
}

.disable-action:hover {
    background-color: #fef2f2;
}

.enable-action {
    color: #059669;
    padding: 4px 8px;
}

.enable-action:hover {
    background-color: #f0fdf4;
}

/* 对话框样式 */
.dialog-footer {
    text-align: right;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 批次管理表格样式 */
.time-info {
    font-size: 12px;
    line-height: 1.4;
}

.created-time {
    color: #6b7280;
}

.updated-time {
    color: #3b82f6;
}

.toggle-disable {
    color: #f59e0b;
    font-size: 12px;
}

.toggle-enable {
    color: #10b981;
    font-size: 12px;
}

/* 提示信息样式 */
.tips-section {
    margin-top: 16px;
    padding: 12px;
    background-color: #f9fafb;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.tip-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin: 0 0 8px 0;
}

.tip-text {
    font-size: 12px;
    color: #6b7280;
    margin: 0 0 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .prize-config-page {
        padding: 16px;
    }

    .page-title {
        font-size: 24px;
    }

    .page-description {
        font-size: 16px;
    }

    .selector-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .summary-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

/* Element UI 组件样式调整 */
.el-table {
    font-size: 12px;
}

.el-table .el-table__header th {
    background-color: #f9fafb;
    color: #374151;
    font-weight: 600;
    padding: 12px 0;
}

.el-table .el-table__body td {
    padding: 8px 0;
}

.el-tag {
    border-radius: 6px;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-card {
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.el-button {
    border-radius: 6px;
}

.el-input,
.el-select {
    border-radius: 6px;
}

.el-dialog {
    border-radius: 12px;
}

.el-dialog__header {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.el-dialog__title {
    font-weight: 600;
    color: #1f2937;
}

.el-input-number {
    width: 100%;
}

/* 自定义按钮样式 */
.el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.el-button--primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.el-button--text {
    color: #3b82f6;
}

.el-button--text:hover {
    color: #2563eb;
    background-color: #eff6ff;
}

/* 时间信息样式 */
.time-info {
    font-size: 12px;
}

.time-row {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.time-label {
    color: #6b7280;
    width: 30px;
    flex-shrink: 0;
}

.time-value {
    color: #374151;
    font-weight: 500;
}

/* 批次过滤器样式 */
.batch-filters {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 缺省页面样式 */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: #fafafa;
    border-radius: 8px;
    margin-top: 20px;
}

.empty-content {
    text-align: center;
    max-width: 400px;
    padding: 40px 20px;
}

.empty-icon {
    margin-bottom: 20px;
}

.empty-icon i {
    font-size: 64px;
    color: #c0c4cc;
}

.empty-title {
    font-size: 18px;
    color: #303133;
    margin: 0 0 12px 0;
    font-weight: 500;
}

.empty-description {
    font-size: 14px;
    color: #909399;
    line-height: 1.6;
    margin: 0 0 24px 0;
}

.empty-actions {
    margin-top: 24px;
}

/* 批次管理弹窗样式 */
.batch-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
}

.batch-filters {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
</style>
