<template>
    <div class="verification-record-page">
        <!-- 统计卡片区域 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.redemption_count }}</div>
                <div class="stat-label">已核销</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.unverified_count }}</div>
                <div class="stat-label">未核销</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ redemptionRate }}%</div>
                <div class="stat-label">核销率</div>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <el-card class="filter-card" shadow="never">
            <el-form
                label-position="top"
                :model="searchForm"
                :inline="true"
                class="search-form"
            >
                <el-form-item label="搜索">
                    <el-input
                        v-model="searchForm.keyword"
                        placeholder="手机号/奖品内容/核销公司"
                        clearable
                        style="width: 280px;"
                        @keyup.enter.native="handleSearch"
                    />
                </el-form-item>
                <el-form-item label="核销状态">
                    <el-select
                        v-model="searchForm.status"
                        placeholder="选择状态"
                        clearable
                    >
                        <el-option label="全部" :value="0" />
                        <el-option label="未核销" :value="1" />
                        <el-option label="已核销" :value="2" />
                        <el-option label="已过期" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="中奖时间">
                    <el-date-picker
                        v-model="searchForm.prizeTimeRange"
                        type="daterange"
                        style="width: 380px;"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    />
                </el-form-item>
                <el-form-item label="核销时间">
                    <el-date-picker
                        v-model="searchForm.redemptionTimeRange"
                        type="daterange"
                        style="width: 380px;"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    />
                </el-form-item>
                <el-form-item label="操作">
                    <div>
                        <el-button type="primary" @click="handleSearch"
                            >搜索</el-button
                        >
                        <el-button @click="handleReset">重置</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 数据表格 -->
        <el-card class="table-card" shadow="never">
            <el-table
                :data="records"
                v-loading="loading"
                stripe
                border
                height="calc(100vh - 400px)"
                @sort-change="handleSortChange"
            >
                <el-table-column
                    prop="phone"
                    label="手机号"
                    width="140"
                    fixed="left"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.phone }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="100"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.short_code }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="prize_name"
                    label="中奖内容"
                    width="180"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <div>
                            <div class="prize-name">
                                {{ scope.row.prize_name }}
                            </div>
                            <div class="product-name">
                                {{ scope.row.goos_name }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="prize_time"
                    label="中奖时间"
                    width="160"
                    sortable="custom"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.prize_time }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="status"
                    label="核销状态"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            :type="getStatusTagType(scope.row.status)"
                            size="small"
                        >
                            {{ getStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="company_name"
                    label="核销公司"
                    min-width="200"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.company_name || "-" }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="redemption_time"
                    label="核销时间"
                    width="160"
                    sortable="custom"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{ scope.row.redemption_time || "-" }}
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                />
            </div>
        </el-card>
    </div>
</template>

<script>
export default {
    name: "VerificationRecord",
    data() {
        return {
            loading: false,

            // 搜索表单
            searchForm: {
                keyword: "",
                status: 0,
                prizeTimeRange: null,
                redemptionTimeRange: null,
                order_column: "",
                order_by: ""
            },

            // 分页
            pagination: {
                page: 1,
                limit: 10,
                total: 0
            },

            // 数据
            records: [],
            stats: {
                total: 0,
                redemption_count: 0,
                unverified_count: 0,
                redemptionRate: 0
            },

            // 核销状态映射
            statusMap: {
                1: { text: "未核销", type: "warning" },
                2: { text: "已核销", type: "success" },
                3: { text: "已过期", type: "danger" }
            }
        };
    },

    computed: {
        // 计算核销率
        redemptionRate() {
            if (this.stats.total === 0) return 0;
            return (
                (this.stats.redemption_count / this.stats.total) *
                100
            ).toFixed(1);
        }
    },

    mounted() {
        this.loadData();
    },

    methods: {
        // 加载数据
        async loadData() {
            this.loading = true;
            try {
                const params = this.buildSearchParams();
                const res = await this.$request.main.getRedemptionRecords(
                    params
                );

                if (res.data && res.data.error_code === 0) {
                    this.records = res.data.data.list || [];
                    this.pagination.total = res.data.data.total || 0;
                    this.stats = {
                        total: res.data.data.total || 0,
                        redemption_count: res.data.data.redemption_count || 0,
                        unverified_count: res.data.data.unverified_count || 0,
                        redemptionRate: this.redemptionRate
                    };
                }
            } catch (error) {
                this.$message.error("加载数据失败");
                console.error(error);
            } finally {
                this.loading = false;
            }
        },

        // 构建搜索参数
        buildSearchParams() {
            const params = {
                keyword: this.searchForm.keyword || "",
                status: this.searchForm.status,
                page: this.pagination.page,
                limit: this.pagination.limit
            };

            // 中奖时间范围
            if (
                this.searchForm.prizeTimeRange &&
                this.searchForm.prizeTimeRange.length === 2
            ) {
                params.prize_start_time = this.searchForm.prizeTimeRange[0];
                params.prize_end_time = this.searchForm.prizeTimeRange[1];
            } else {
                params.prize_start_time = "";
                params.prize_end_time = "";
            }

            // 核销时间范围
            if (
                this.searchForm.redemptionTimeRange &&
                this.searchForm.redemptionTimeRange.length === 2
            ) {
                params.redemption_start_time = this.searchForm.redemptionTimeRange[0];
                params.redemption_end_time = this.searchForm.redemptionTimeRange[1];
            } else {
                params.redemption_start_time = "";
                params.redemption_end_time = "";
            }

            // 排序参数
            if (this.searchForm.order_column) {
                params.order_column = this.searchForm.order_column;
                params.order_by = this.searchForm.order_by || "desc";
            }

            return params;
        },

        // 搜索
        handleSearch() {
            this.pagination.page = 1;
            this.loadData();
        },

        // 重置
        handleReset() {
            this.searchForm = {
                keyword: "",
                status: 0,
                prizeTimeRange: null,
                redemptionTimeRange: null,
                order_column: "",
                order_by: ""
            };
            this.pagination.page = 1;
            this.loadData();
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.limit = val;
            this.pagination.page = 1;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.page = val;
            this.loadData();
        },

        // 排序改变
        handleSortChange({ prop, order }) {
            // 如果取消排序（order为null），清空排序字段
            if (!order) {
                this.searchForm.order_column = "";
                this.searchForm.order_by = "";
            } else {
                // 设置排序字段
                if (prop === "prize_time") {
                    this.searchForm.order_column = "prize_time";
                } else if (prop === "redemption_time") {
                    this.searchForm.order_column = "redemption_time";
                } else {
                    this.searchForm.order_column = "";
                }

                this.searchForm.order_by =
                    order === "ascending" ? "asc" : "desc";
            }

            this.loadData();
        },

        // 工具方法
        getStatusText(status) {
            return this.statusMap[status]?.text || status;
        },

        getStatusTagType(status) {
            return this.statusMap[status]?.type || "info";
        }
    }
};
</script>

<style scoped>
.verification-record-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 统计卡片样式 */
.stats-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.stat-card .stat-number {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-card .stat-label {
    font-size: 12px;
    line-height: 1;
    color: #666;
}

/* 不同状态的颜色 */
.stat-card:nth-child(1) .stat-number {
    color: #409eff; /* 蓝色 - 总记录数 */
}

.stat-card:nth-child(2) .stat-number {
    color: #67c23a; /* 绿色 - 已核销 */
}

.stat-card:nth-child(3) .stat-number {
    color: #e6a23c; /* 橙色 - 未核销 */
}

.stat-card:nth-child(4) .stat-number {
    color: #9c27b0; /* 紫色 - 核销率 */
}

.filter-card {
    margin-bottom: 20px;
}

.search-form {
    margin-bottom: 0;
}

.search-actions {
    text-align: left;
    margin-top: 10px;
}

.table-card {
    margin-bottom: 20px;
}

.prize-name {
    font-weight: bold;
    color: #333;
}

.product-name {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

.pagination-wrapper {
    margin-top: 20px;
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-cards {
        gap: 15px;
    }

    .stat-card {
        min-width: 180px;
    }
}

@media (max-width: 768px) {
    .verification-record-page {
        padding: 10px;
    }

    .stats-cards {
        flex-direction: column;
        gap: 15px;
    }

    .stat-card {
        min-width: auto;
        padding: 15px;
    }

    .stat-card .stat-number {
        font-size: 24px;
    }

    .search-actions {
        text-align: left;
    }

    .pagination-wrapper {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .stats-cards {
        gap: 10px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-card .stat-number {
        font-size: 20px;
    }
}

/* Element UI 组件样式调整 */
.el-table {
    font-size: 14px;
}

.el-table .el-table__header th {
    background-color: #fafafa;
    color: #333;
    font-weight: bold;
}

.el-tag {
    border-radius: 4px;
}

.el-descriptions {
    margin-top: 10px;
}

.el-form-item {
    margin-bottom: 18px;
}

.el-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-button {
    border-radius: 4px;
}

.el-input,
.el-select {
    border-radius: 4px;
}

.el-dialog {
    border-radius: 8px;
}

.el-dialog__header {
    background-color: #fafafa;
    border-bottom: 1px solid #eee;
}

.el-dialog__title {
    font-weight: bold;
    color: #333;
}
::v-deep .el-form--label-top .el-form-item__label {
    padding: 0;
}
</style>
