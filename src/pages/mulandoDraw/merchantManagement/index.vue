<template>
    <div class="merchant-manage-layout">
        <!-- 搜索表单 -->
        <div class="search-form">
            <el-card>
                <div class="search-header">
                    <el-input
                        v-model="searchText"
                        placeholder="搜索手机号、公司名称、联系人或地址"
                        clearable
                        style="width: 400px;"
                        @input="handleSearch"
                        @clear="handleSearch"
                    >
                        <el-button
                            slot="append"
                            icon="el-icon-search"
                            @click="handleSearch"
                        >
                            搜索
                        </el-button>
                    </el-input>
                </div>
            </el-card>
        </div>

        <!-- 商家列表表格 -->
        <div class="table-container">
            <el-card>
                <el-table
                    :data="paginatedMerchants"
                    style="width: 100%"
                    v-loading="loading"
                    row-key="id"
                    @sort-change="handleSortChange"
                    :max-height="1000"
                    empty-text="暂无商家数据"
                >
                    <!-- 手机号码列 -->
                    <el-table-column
                        prop="phone"
                        label="手机号码"
                        width="140"
                        fixed="left"
                        :filters="phoneFilters"
                        :filter-method="filterPhone"
                        filter-placement="bottom-end"
                    >
                        <template slot="header">
                            <span>手机号码</span>
                            <el-tooltip
                                content="点击筛选按钮可按手机号筛选"
                                placement="top"
                            >
                                <i
                                    class="el-icon-info"
                                    style="margin-left: 4px; color: #999;"
                                ></i>
                            </el-tooltip>
                        </template>
                    </el-table-column>

                    <!-- 公司名称列 -->
                    <el-table-column
                        prop="companyName"
                        label="公司名称"
                        width="240"
                        sortable="custom"
                        show-overflow-tooltip
                    >
                        <template slot-scope="scope">
                            <el-tooltip
                                :content="scope.row.companyName"
                                placement="top-start"
                            >
                                <span>{{ scope.row.companyName }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>

                    <!-- 联系人姓名列 -->
                    <el-table-column
                        prop="contactName"
                        label="联系人姓名"
                        width="150"
                    ></el-table-column>

                    <!-- 地址列 -->
                    <el-table-column
                        prop="address"
                        label="地址"
                        min-width="280"
                    >
                    </el-table-column>

                    <!-- 营业执照列 -->
                    <el-table-column
                        label="营业执照"
                        width="140"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                icon="el-icon-view"
                                @click="
                                    handleViewLicense(scope.row.businessLicense)
                                "
                            >
                                查看
                            </el-button>
                        </template>
                    </el-table-column>

                    <!-- 审核状态列 -->
                    <el-table-column
                        prop="auditStatus"
                        label="审核状态"
                        width="140"
                        align="center"
                        :filters="auditStatusFilters"
                        :filter-method="filterAuditStatus"
                        filter-placement="bottom-end"
                    >
                        <template slot-scope="scope">
                            <el-tag
                                :type="
                                    getAuditStatusType(scope.row.auditStatus)
                                "
                            >
                                {{ getAuditStatusText(scope.row.auditStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <!-- 操作列 -->
                    <el-table-column
                        label="操作"
                        width="160"
                        fixed="right"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <!-- 待审核状态显示通过和驳回按钮 -->
                            <template v-if="scope.row.auditStatus === 2">
                                <el-button
                                    type="text"
                                    size="small"
                                    icon="el-icon-check"
                                    style="color: #67c23a;"
                                    @click="handleApprove(scope.row)"
                                >
                                    通过
                                </el-button>
                                <el-button
                                    type="text"
                                    size="small"
                                    icon="el-icon-close"
                                    style="color: #f56c6c;"
                                    @click="handleReject(scope.row)"
                                >
                                    驳回
                                </el-button>
                            </template>
                            <!-- 已驳回状态显示驳回原因 -->
                            <template v-else-if="scope.row.auditStatus === 4">
                                <el-tooltip placement="top">
                                    <template #content>
                                        <div style="width: 300px;">
                                            {{ scope.row.reason }}
                                        </div>
                                    </template>
                                    <el-button
                                        type="text"
                                        size="small"
                                        icon="el-icon-info"
                                        style="color: #909399;"
                                    >
                                        查看原因
                                    </el-button>
                                </el-tooltip>
                            </template>
                            <!-- 已通过状态不显示操作按钮 -->
                            <template v-else>
                                <span style="color: #909399;">无操作</span>
                            </template>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </el-card>
        </div>

        <!-- 营业执照查看对话框 -->
        <el-dialog
            title="营业执照"
            :visible.sync="imageModalVisible"
            width="800px"
            :close-on-click-modal="false"
        >
            <div style="text-align: center;">
                <el-image
                    :src="currentImage"
                    alt="营业执照"
                    style="max-width: 100%; max-height: 500px;"
                    fit="contain"
                    :preview-src-list="[currentImage]"
                >
                    <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                        <p>图片加载失败</p>
                    </div>
                </el-image>
            </div>
        </el-dialog>

        <!-- 驳回确认对话框 -->
        <el-dialog
            title="确认驳回"
            :visible.sync="rejectModalVisible"
            width="500px"
            :close-on-click-modal="false"
        >
            <p>
                确定要驳回商家
                <strong>{{
                    currentMerchant && currentMerchant.companyName
                }}</strong>
                的申请吗？
            </p>
            <p style="color: #f56c6c;">此操作不可撤销。</p>

            <!-- 驳回原因输入 -->
            <div style="margin-top: 20px;">
                <label
                    style="display: block; margin-bottom: 8px; font-weight: bold;"
                >
                    驳回原因 <span style="color: #f56c6c;">*</span>
                </label>
                <el-input
                    v-model="rejectReason"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入驳回原因..."
                    maxlength="200"
                    show-word-limit
                ></el-input>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="rejectModalVisible = false">取消</el-button>
                <el-button type="danger" @click="confirmReject"
                    >确认驳回</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import mainApi from "@/services/main/main.js";

export default {
    name: "MerchantManagement",
    data() {
        return {
            loading: false,
            searchText: "",
            merchants: [],
            sortedInfo: {},
            imageModalVisible: false,
            currentImage: "",
            rejectModalVisible: false,
            currentMerchant: null,
            rejectReason: "", // 驳回原因
            pagination: {
                currentPage: 1,
                pageSize: 10
            },
            total: 0, // 总数据量
            // 审核状态映射 - 根据API返回的状态码映射
            auditStatusMap: {
                2: { text: "待审核", type: "warning" },
                3: { text: "已通过", type: "success" },
                4: { text: "已驳回", type: "danger" }
            }
        };
    },
    computed: {
        // 搜索过滤后的商家列表 - 现在使用服务端搜索，这里直接返回
        filteredMerchants() {
            return this.merchants;
        },
        // 分页后的商家列表 - 现在使用服务端分页，这里直接返回
        paginatedMerchants() {
            return this.merchants;
        },
        // 手机号筛选选项
        phoneFilters() {
            const phones = [...new Set(this.merchants.map(m => m.phone))];
            return phones.map(phone => ({ text: phone, value: phone }));
        },
        // 审核状态筛选选项
        auditStatusFilters() {
            return [
                { text: "已通过", value: 3 },
                { text: "待审核", value: 2 },
                { text: "已驳回", value: 4 }
            ];
        }
    },
    mounted() {
        this.loadMerchants();
    },
    methods: {
        // 加载商家数据
        async loadMerchants() {
            this.loading = true;
            try {
                const params = {
                    keyword: this.searchText || "",
                    order_column:
                        this.sortedInfo.prop === "companyName"
                            ? "company_name"
                            : "",
                    order_by:
                        this.sortedInfo.order === "ascending" ? "asc" : "desc",
                    page: this.pagination.currentPage,
                    limit: this.pagination.pageSize
                };

                const response = await mainApi.getMerchantList(params);

                if (response.data && response.data.data) {
                    // 转换API数据格式为组件需要的格式
                    this.merchants = response.data.data.list.map(item => ({
                        id: item.id,
                        phone: item.phone,
                        companyName: item.company_name,
                        contactName: item.contact_name,
                        unifiedSocialCode: item.unified_social_code,
                        businessLicense: item.business_license_image,
                        address: `${item.province_name}${item.city_name}${item.district_name}${item.address}`,
                        auditStatus: item.status,
                        reason: item.reason,
                        createdAt: item.create_time,
                        lastLoginTime: item.last_login_time
                    }));
                    this.total = response.data.data.total;

                    console.log(
                        "商家数据加载成功:",
                        this.merchants.length,
                        "条记录"
                    );
                } else {
                    this.merchants = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("加载商家数据失败:", error);
                this.$message.error("加载商家数据失败");
                this.merchants = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        // 搜索处理
        handleSearch() {
            this.pagination.currentPage = 1;
            this.loadMerchants(); // 重新加载数据
        },
        // 表格排序处理
        handleSortChange(sortInfo) {
            this.sortedInfo = sortInfo;
            this.loadMerchants(); // 重新加载数据
        },
        // 查看营业执照
        handleViewLicense(imageUrl) {
            this.currentImage = imageUrl;
            this.imageModalVisible = true;
        },
        // 通过审核
        async handleApprove(merchant) {
            try {
                await this.$confirm(
                    `确定要通过商家"${merchant.companyName}"的申请吗？`,
                    "确认通过",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    }
                );

                const data = {
                    id: merchant.id,
                    is_pass: true,
                    reason: ""
                };

                await mainApi.merchantReview(data);

                this.$message.success("商家审核通过");

                // 重新加载数据
                this.loadMerchants();
            } catch (error) {
                if (error !== "cancel") {
                    console.error("通过商家审核失败:", error);
                    this.$message.error("通过商家审核失败");
                }
            }
        },
        // 驳回商家
        handleReject(merchant) {
            this.currentMerchant = merchant;
            this.rejectReason = ""; // 重置驳回原因
            this.rejectModalVisible = true;
        },
        // 确认驳回
        async confirmReject() {
            if (!this.currentMerchant) return;

            if (!this.rejectReason.trim()) {
                this.$message.warning("请输入驳回原因");
                return;
            }

            try {
                const data = {
                    id: this.currentMerchant.id,
                    is_pass: false,
                    reason: this.rejectReason.trim()
                };

                await mainApi.merchantReview(data);

                this.$message.success("商家已驳回");
                this.rejectModalVisible = false;
                this.currentMerchant = null;
                this.rejectReason = "";

                // 重新加载数据
                this.loadMerchants();
            } catch (error) {
                console.error("驳回商家失败:", error);
                this.$message.error("驳回商家失败");
            }
        },
        // 获取审核状态文本
        getAuditStatusText(status) {
            return this.auditStatusMap[status]?.text || "未知";
        },
        // 获取审核状态类型
        getAuditStatusType(status) {
            return this.auditStatusMap[status]?.type || "info";
        },
        // 手机号筛选
        filterPhone(value, row) {
            return row.phone === value;
        },
        // 审核状态筛选
        filterAuditStatus(value, row) {
            return row.auditStatus === value;
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.currentPage = 1;
            this.loadMerchants(); // 重新加载数据
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.loadMerchants(); // 重新加载数据
        }
    }
};
</script>

<style scoped>
.merchant-manage-layout {
    padding: 20px;
}

.search-form {
    margin-bottom: 20px;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.page-title {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    color: #303133;
}

.table-container {
    background: #fff;
}

.image-slot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 200px;
    background: #f5f7fa;
    color: #909399;
}

.image-slot i {
    font-size: 30px;
    margin-bottom: 10px;
}

.image-slot p {
    margin: 0;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .merchant-manage-layout {
        padding: 10px;
    }

    .search-header {
        flex-direction: column;
        align-items: stretch;
    }

    .search-header .el-input {
        width: 100% !important;
    }

    .page-title {
        font-size: 20px;
        text-align: center;
        margin-bottom: 10px;
    }
}

/* 表格样式优化 */
.el-table {
    border-radius: 4px;
    overflow: hidden;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-table td {
    border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover > td {
    background-color: #f5f7fa;
}

/* 标签样式 */
.el-tag {
    border-radius: 4px;
    font-weight: 500;
}

/* 按钮样式 */
.el-button--text {
    padding: 0;
    border: none;
    color: #409eff;
    background: transparent;
}

.el-button--text:hover {
    color: #66b1ff;
    background: transparent;
}

/* 对话框样式 */
.el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
}

.el-dialog__body {
    padding: 20px;
}

.dialog-footer {
    text-align: right;
    padding: 10px 20px 20px;
}

/* 分页样式 */
.el-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
</style>
