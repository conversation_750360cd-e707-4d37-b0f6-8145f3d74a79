<template>
  <div class="hotel-room-list">
    <div class="search-bar">
      <el-input
        v-model="query.name"
        placeholder="请输入房间名称"
        style="width: 200px; margin-right: 10px;"
      ></el-input>
      <el-select
        v-model="query.hotel_id"
        placeholder="请选择酒店名称"
        style="width: 200px; margin-right: 10px;"
      >
        <el-option
          v-for="item in hotelOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button type="success" @click="dialogStatus = true">新增</el-button>
    </div>

    <el-row v-if="roomList.length" :gutter="20" class="room-list">
      <el-col :span="12" v-for="(room) in roomList" :key="room.id" style="margin-bottom: 10px; padding: 0 5px ; min-width: 420px;">
        <el-card class="room-card"  :body-style="{padding: '10px', width:'100%',  }">
          <div class="room-content" @click="viewDetails(room)" style="display: flex;">
            <div class="room-image" style="margin-right: 5px;">
              <img :src="room.title_image[0]" alt="房间图片" style="width: 100%; height: 100%; object-fit: cover;">
            </div>
            <div class="room-info">
              <div>
                <h5 style="color: #f2571f; margin-top: 0;">{{ room.hotel_name}}</h5>
                <div>房间名称：{{ room.room_name }}</div>
                <div>床型：{{ room.bed_type }}</div>
                <div>面积：{{ room.room_area }}m²</div>
                <div>房间数：{{ room.room_count }}</div>
              </div>
              <div class="btn-view" style="align-self: flex-end;">
                <el-button type="text" style="color: #f2571f;" @click="viewDetails(room)">详情</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else></el-empty>
    <div class="pagination-block">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="query.page"
        :page-size="query.limit"
        :page-sizes="[10, 30, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
            :visible.sync="dialogStatus"
            title="房间信息"
            custom-class="dialogwid"
            width="90%"
            height="100%"
        >
            <roomUpdate
                v-if="dialogStatus"
                ref="editForm"
                :parentObj="this"
                @close="close"
            ></roomUpdate>
        </el-dialog>
  </div>
</template>

<script>
import roomUpdate from './roomUpdate.vue';
export default {
  components: { roomUpdate },
  data() {
    return {
      query: {
                page: 1,
                limit: 10,
                name: "",
                hotel_id:"",
            },
      hotelOptions: [],
      roomList: [],
     
      dialogStatus:false,
      total: 0
    };
  },
  mounted() {
       this.fetchRoomList();
       this.getHotelListReq();
      
   },
  methods: {
    async fetchRoomList(){
      let res = await this.$request.main.getHotelRoomsList(this.query);
            console.log("列表", res);
            if (res.data.error_code == 0) {
                this.roomList = res.data.data.list;
                this.total = res.data.data.total;
            }
    },
    
    async getHotelListReq() {
            let res = await this.$request.main.getHotelList({page:1, limit:999});
           
            if (res.data.error_code == 0) {
                this.hotelOptions = res.data.data.list;
               
            }
        },
    handleSearch() {
      this.fetchRoomList();
    },
    handleAdd() {
      // 实现新增房间的逻辑
    },
    viewDetails(row) {
      // 实现查看详情的逻辑
      this.dialogStatus = true;
     
      this.$nextTick(() => {
          this.$refs.editForm.openForm(JSON.parse(JSON.stringify(row)));
      });
    },
    close() {
            this.fetchRoomList();
            this.dialogStatus = false;
        },
    handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.fetchRoomList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.fetchRoomList();
        }
    }
  };
  
</script>
<style lang="scss" scoped>
.room-list{
  margin-top: 20px;
  margin-bottom: 20px;
}
.room-card{
  display: flex;
}
.room-image{
  height: 6rem;
}
.room-image img {
  // width: 100%;
  // height: 8rem;
  object-fit: cover;
  aspect-ratio: 16/9;
}
.room-info {
  display: flex;
  justify-content: space-between;
  flex: 1;
  padding-left: 5px;
  position: relative;
}
.btn-view {
    display: flex;
    flex-direction: column-reverse;
}
</style>