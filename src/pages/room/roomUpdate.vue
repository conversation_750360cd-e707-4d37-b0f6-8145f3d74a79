<template>
    <div>
        <el-form ref="form" :model="datas" label-width="100px" >
            <el-divider content-position="left" >基本信息</el-divider>
            <el-form-item label="房间名称" prop="room_name">
                <el-col :span="12">
                    <el-input
                        v-model="datas.room_name"
                        placeholder="请输入房间名称"
                    />
                </el-col>
             </el-form-item>
            <div  style="display: flex;flex-wrap: wrap;">
                <el-form-item label="房间面积" prop="room_area">
                    <el-input-number :controls="false" :min="0" v-model="datas.room_area" ></el-input-number> m²
            </el-form-item>
            <el-form-item label="楼层" prop="storey">
                <el-input
                v-model="datas.storey"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item label="兑换消耗" prop="rabbit">
                <el-input-number :controls="false" :min="0" v-model="datas.rabbit" ></el-input-number> 兔头
            </el-form-item>
            </div>
            <div  style="display: flex;flex-wrap: wrap;">
            <el-form-item label="所属酒店" prop="hotel_id">
                <el-select
                    v-model="datas.hotel_id"
                    placeholder="请选择酒店名称"
                    style="width: 200px; margin-right: 10px;"
                >
                    <el-option
                    v-for="item in hotelOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            
            <el-form-item label="房间数" prop="room_count">
                <el-input-number :controls="false" :min="0" v-model="datas.room_count" ></el-input-number>
            </el-form-item>
            <el-form-item label="有窗" prop="is_window">
                <el-checkbox v-model="datas.is_window" :true-label="1" :false-label="0"></el-checkbox>
              </el-form-item>
              <el-form-item label="早餐" prop="is_breakfast">
                <el-checkbox v-model="datas.is_breakfast" :true-label="1" :false-label="0"></el-checkbox>
              </el-form-item>
            </div>
            <el-divider content-position="left" >床型信息</el-divider>
            <el-form-item label="床型" prop="bed_type">
                <el-input
                class="w-normal"
                v-model="datas.bed_type"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item label="床数" prop="bed_count">
                    <el-input-number  class="w-normal" :controls="false" :min="0" v-model="datas.bed_count" ></el-input-number> 
            </el-form-item>
            <el-divider content-position="left" >房间描述</el-divider>
            <el-form-item label="内容" prop="description">
                <Tinymce
                    ref="editor"
                    v-model.trim="datas.description"
                    @singleValidate="singleValidate"
                    :height="200"
                />
            </el-form-item>
            <el-divider content-position="left" >房间图片</el-divider>
            <el-form-item label="题图" prop="title_image">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="datas.title_image"
                    :limit="1"
                   
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-divider content-position="left" >房间施设</el-divider>
          
            <el-form-item  v-for="(config, index) in facilityConfigList" :key="config.id" :label="config.name" prop="facility_config">
                <el-select
                    v-model="relationConfig[index]"
                    clearable
                    filterable
                    multiple
                    placeholder="请选择"
                    style="width: 60%;"
                >
                    <el-option
                        v-for="item in facilityConfigList[index].items"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
           
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
    </div>
</template>
<script>
import Tinymce from "@/components/Tinymce";
import vosOss from "vos-oss";

export default {
    props: {
        parentObj: Object
    },
    components: {
        vosOss,
        Tinymce
    },
    data() {
        const checkTitleMap = (rules, value, callback) => {
            if (this.imageList.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/mulando-hotel/",
            isEdit:false,
            datas: {
                title_image: [],
                room_name: "",
                room_area: "",
                room_count: "",
                rabbit: "",
                bed_type: "",
                bed_count: "",
                hotel_id:"",
                storey: "",
                is_window: 0,
                is_breakfast: 0,
                description: "",
                facility_config: [],
                id:""
            },
            relationConfig:[
                [],
                [],
                [],
                [],
                []
            ],
            hotelOptions:[],
            facilityConfigList:[],
            // imageList:[],
            cateOption: [],
            rules: {
               
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                topic_id: [
                    {
                        required: true,
                        message: "请选择课题",
                        trigger: "blur"
                    }
                ],
                subtitle: [
                    {
                        required: true,
                        message: "请输入副标题",
                        trigger: "blur"
                    }
                ],
                content: [
                    {
                        required: true,
                        message: "请输入详细内容",
                        trigger: "blur"
                    }
                ],
                cover_img: [
                    {
                        required: true,
                        validator: checkTitleMap
                    }
                ],
                author: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur"
                    }
                ],
            },
        };
    },
    mounted() {
        this.getHotelListReq();
        this.getConfigReq();
    },
    methods: {
        async getConfigReq(){
      let res = await this.$request.main.getConfigList();
            if (res.data.error_code == 0) {
                this.facilityConfigList = res.data.data.list;
            }
    },
    async getHotelListReq() {
            let res = await this.$request.main.getHotelList({page:1, limit:999});
           
            if (res.data.error_code == 0) {
                this.hotelOptions = res.data.data.list;
               
            }
        },
        async submits() {
            if (this.validateForm()) {
                this.datas.facility_config = this.relationConfig.reduce((a, b)=>a.concat(b));
                console.log(this.datas);
                let isEditName = "";
                if (this.isEdit) {
                    isEditName = "editHotelRoom";
                } else {
                    isEditName = "addHotelRoom";
                }
               
                this.$request.main[isEditName](this.datas)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.$emit("close");
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        }
                    });
            }
        },
       
        openForm(row) {
            if (row) {
                this.isEdit = true;
                this.datas.title_image= row.title_image;
                this.datas.room_name= row.room_name;
                this.datas.room_area= row.room_area;
                this.datas.room_count= row.room_count;
                this.datas.rabbit= row.rabbit;
                this.datas.bed_type= row.bed_type;
                this.datas.bed_count= row.bed_count;
                this.datas.hotel_id= row.hotel_id;
                this.datas.storey= row.storey;
                this.datas.is_window= row.is_window;
                this.datas.is_breakfast= row.is_breakfast;
                this.datas.id= row.id;
                this.datas.description= row.description;
                this.relationConfig = row.facility_config.map(facility => facility.config.map(item => item.id));
            }
        },

        // clearform() {
        //     this.datas =  this.$options.data().datas;
        // },
        singleValidate() {
            let flag = null;
            this.$refs["form"].validateField("info", valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>
