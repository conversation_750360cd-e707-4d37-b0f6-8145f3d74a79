<template>
    <div class="distributor-list">
        <!-- 搜索区域 -->
        <div class="search-area">
            <div>
                <el-select
                    class="search"
                    v-model="filterType"
                    placeholder="类型"
                    style="width: 120px"
                    clearable
                >
                    <el-option label="战略合作商" :value="1"></el-option>
                    <el-option label="品牌合作商" :value="2"></el-option>
                </el-select>
                <el-select
                    class="search"
                    v-model="expired"
                    placeholder="过期状态"
                    style="width: 120px"
                    clearable
                >
                    <el-option label="已过期" :value="1"></el-option>
                    <el-option label="未过期" :value="0"></el-option>
                </el-select>
                <el-select
                    class="search"
                    v-model="filterStatus"
                    placeholder="证书状态"
                    style="width: 120px"
                    clearable
                >
                    <el-option label="启用" :value="1"></el-option>
                    <el-option label="禁用" :value="0"></el-option>
                </el-select>
                <el-select
                    class="search"
                    v-model="filterAudit"
                    placeholder="审核状态"
                    style="width: 120px"
                    clearable
                >
                    <el-option label="待审核" :value="0"></el-option>
                    <el-option label="已审核" :value="1"></el-option>
                    <el-option label="未通过" :value="2"></el-option>
                </el-select>
                <el-input
                    class="search"
                    v-model="searchQuery"
                    placeholder="搜索名称或地址..."
                    style="width: 300px"
                    clearable
                >
                </el-input>
                <el-button class="search" type="primary" @click="handleSearch"
                    >搜索</el-button
                >
                <el-button type="success" class="search" @click="add"
                    >添加</el-button
                >
            </div>

            <div class="filter-items">
                <el-button type="warning" class="export" @click="exportData"
                    >导出数据</el-button
                >
            </div>
        </div>

        <!-- 表格区域 -->
        <el-table
            :data="tableData"
            border
            style="width: 100%"
            v-loading="loading"
        >
            <el-table-column prop="code" label="编号" width="300">
                <template slot-scope="scope">
                    <div
                        class="code-cell"
                        style="display: flex; justify-content: space-between;"
                    >
                        <el-link
                            type="primary"
                            @click="openNewWindow(scope.row.code)"
                            style="margin-right: 10px;"
                        >
                            {{ scope.row.code }}
                        </el-link>
                        <el-button
                            type="text"
                            size="small"
                            @click="showQrcode(scope.row)"
                        >
                            二维码
                        </el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="name"
                label="名称"
                min-width="220"
            ></el-table-column>
            <el-table-column prop="type" label="类型" width="100">
                <template slot-scope="scope">
                    {{ typeMap[scope.row.type] }}
                </template>
            </el-table-column>
            <el-table-column
                prop="address"
                label="经营地址"
                min-width="250"
            ></el-table-column>
            <el-table-column label="联系方式" width="200">
                <template slot-scope="scope">
                    <div>{{ scope.row.phone }}</div>
                    <div>{{ scope.row.email }}</div>
                </template>
            </el-table-column>
            <el-table-column label="授权期限" width="140">
                <template slot-scope="scope">
                    <div>
                        {{ scope.row.startDate }}
                    </div>
                    <div>
                        {{ scope.row.endDate }}
                    </div>
                    <el-tag
                        :type="scope.row.expired ? 'danger' : 'success'"
                        size="mini"
                        style="margin-top: 5px"
                    >
                        {{ scope.row.expired ? "已过期" : "未过期" }}
                    </el-tag>
                    <el-tag
                        :type="getAuditStatusType(scope.row.is_audit)"
                        size="mini"
                        style="margin-top: 5px;margin-left: 4px"
                    >
                        {{ getAuditStatusLabel(scope.row.is_audit) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                    <el-button
                        :type="scope.row.status === '1' ? 'danger' : 'success'"
                        size="mini"
                        @click="handleStatusChange(scope.row)"
                    >
                        {{ scope.row.status === "1" ? "禁用" : "启用" }}
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column
                prop="visits"
                label="扫码次数"
                width="80"
            ></el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        size="mini"
                        @click="handleEdit(scope.row)"
                    >
                        编辑
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 表区域 -->
        <div class="charts-container">
            <el-card class="chart-card">
                <div slot="header">
                    <span>经销商类型分布</span>
                </div>
                <div ref="pieChart" class="chart"></div>
            </el-card>

            <el-card class="chart-card">
                <div slot="header">
                    <span>各省份经销商数量 Top 10</span>
                </div>
                <div ref="barChart" class="chart"></div>
            </el-card>
        </div>

        <!-- 分页 -->

        <!-- 在 template 最后添加弹窗组件 -->
        <el-dialog
            :title="isEdit ? '编辑经销商' : '添加经销商'"
            :visible.sync="dialogVisible"
            width="1100px"
            :close-on-click-modal="false"
        >
            <div class="dialog-content">
                <!-- 左侧表单 -->
                <div class="form-section">
                    <el-form
                        :model="form"
                        :rules="rules"
                        ref="form"
                        label-width="120px"
                    >
                        <el-form-item
                            label="公司名称"
                            prop="company_name"
                            style="width: 500px;"
                        >
                            <el-input v-model="form.company_name"></el-input>
                        </el-form-item>
                        <el-form-item label="经销商类型" prop="dealer_type">
                            <el-select
                                v-model="form.dealer_type"
                                placeholder="请选择"
                            >
                                <el-option
                                    label="战略合作商"
                                    :value="1"
                                ></el-option>
                                <el-option
                                    label="品牌合作商"
                                    :value="2"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="授权区域" required>
                            <el-row :gutter="10">
                                <el-col :span="6">
                                    <el-form-item prop="province_code">
                                        <el-select
                                            v-model="form.province_code"
                                            placeholder="请选择省份"
                                            @change="handleProvinceChange"
                                        >
                                            <el-option
                                                v-for="item in provinceOptions"
                                                :key="item.code"
                                                :label="item.name"
                                                :value="Number(item.code)"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item prop="city_code">
                                        <el-select
                                            v-model="form.city_code"
                                            placeholder="请选择城市"
                                            :disabled="!form.province_code"
                                            @change="handleCityChange"
                                            @clear="handleCityClear"
                                            clearable
                                        >
                                            <el-option
                                                v-for="item in cityOptions"
                                                :key="item.code"
                                                :label="item.name"
                                                :value="Number(item.code)"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item prop="area_code">
                                        <el-select
                                            v-model="form.area_code"
                                            placeholder="请选择区域"
                                            :disabled="!form.city_code"
                                            @clear="handleAreaClear"
                                            clearable
                                        >
                                            <el-option
                                                v-for="item in areaOptions"
                                                :key="item.code"
                                                :label="item.name"
                                                :value="Number(item.code)"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form-item>
                        <el-form-item
                            label="经营地址"
                            prop="address"
                            style="width: 500px;"
                        >
                            <el-input v-model="form.address"></el-input>
                        </el-form-item>
                        <el-form-item
                            label="电话"
                            prop="phone"
                            style="width: 300px;"
                        >
                            <el-input v-model="form.phone"></el-input>
                        </el-form-item>
                        <el-form-item
                            label="邮箱"
                            prop="email"
                            style="width: 300px;"
                        >
                            <el-input v-model="form.email"></el-input>
                        </el-form-item>
                        <el-form-item
                            label="业务员"
                            prop="salesman_id"
                            style="width: 300px;"
                        >
                            <el-select
                                v-model="form.salesman_id"
                                placeholder="请选择业务员"
                                clearable
                                filterable
                            >
                                <el-option
                                    v-for="item in salesmanOptions"
                                    :key="item.userid"
                                    :label="item.realname"
                                    :value="item.userid"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="授权期限" prop="license_time">
                            <el-date-picker
                                v-model="form.license_time"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 右侧预览区域 -->
                <div class="preview-section">
                    <div class="preview-title">证书预览</div>
                    <div class="preview-container">
                        <img
                            :src="previewImageUrl"
                            class="preview-image"
                            alt="证书预览"
                            v-if="previewImageUrl"
                        />
                        <div class="no-preview" v-else>
                            暂无预览图片
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 修改二维码弹窗部分 -->
        <el-dialog
            title="经销商二维码"
            :visible.sync="qrcodeVisible"
            width="400px"
            :close-on-click-modal="false"
            custom-class="qrcode-dialog"
        >
            <div class="qrcode-content">
                <div class="qrcode-wrapper">
                    <img
                        v-if="qrcodeUrl"
                        :src="qrcodeUrl"
                        alt="Certificate QR Code"
                        class="qrcode-image"
                    />
                    <div v-else class="qrcode-loading">
                        <i class="el-icon-loading"></i>
                        <span>加载中...</span>
                    </div>
                </div>
                <div class="qrcode-tips">
                    <p>扫描二维码可查看证书详情</p>
                    <el-button
                        type="primary"
                        size="small"
                        @click="downloadQRCode"
                        :disabled="!qrcodeUrl"
                    >
                        <i class="el-icon-download"></i>
                        下载二维码
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import Cookies from "js-cookie";
import * as echarts from "echarts";

export default {
    name: "DistributorList",
    data() {
        return {
            searchQuery: "",
            filterType: "",
            filterStatus: "",
            filterAudit: "",
            loading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            pieChart: null,
            barChart: null,
            typeMap: {
                "1": "战略合作商",
                "2": "品牌合作商"
            },
            statusMap: {
                "1": "启用",
                "0": "禁用"
            },
            auditStatusMap: {
                0: "待审核",
                1: "已审核",
                2: "未通过"
            },
            query: {
                page: 1,
                pageSize: 10,
                search: "",
                dealer_type: "",
                license_status: "",
                expired: "",
                is_audit: ""
            },
            dialogVisible: false,
            form: {
                dealer_code: "",
                company_name: "",
                dealer_type: "",
                address: "",
                phone: "",
                email: "",
                license_time: [],
                province_code: "",
                city_code: "",
                area_code: "",
                salesman_id: ""
            },
            rules: {
                dealer_code: [
                    {
                        required: true,
                        message: "请输入经销商代码",
                        trigger: "blur"
                    }
                ],
                company_name: [
                    {
                        required: true,
                        message: "请输入公司名",
                        trigger: "blur"
                    }
                ],
                dealer_type: [
                    {
                        required: true,
                        message: "请选择经销商类型",
                        trigger: "change"
                    }
                ],
                address: [
                    { required: true, message: "请输入地址", trigger: "blur" }
                ],
                phone: [
                    { required: true, message: "请输入电话", trigger: "blur" }
                ],
                email: [
                    {
                        type: "email",
                        message: "请输入正确的邮箱地址",
                        trigger: "blur"
                    }
                ],
                license_time: [
                    {
                        required: true,
                        message: "请选择授权期限",
                        trigger: "change"
                    }
                ],
                province_code: [
                    { required: true, message: "请选择省份", trigger: "change" }
                ],
                salesman_id: [
                    {
                        required: true,
                        message: "请选择业务员",
                        trigger: "change"
                    }
                ]
            },
            provinceOptions: [], // 省份选项
            cityOptions: [], // 城市选项
            areaOptions: [], // 区域选项
            qrcodeVisible: false,
            qrcodeUrl: "",
            isEdit: false,
            editingRow: null,
            previewImageUrl: "", // 替��之前的 selectedImage
            expired: "",
            salesmanOptions: []
        };
    },
    created() {
        this.getDistributorList();
        this.updateCharts();
    },
    mounted() {
        this.initCharts();
        window.addEventListener("resize", this.resizeCharts);
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.resizeCharts);
        if (this.pieChart) {
            this.pieChart.dispose();
        }
        if (this.barChart) {
            this.barChart.dispose();
        }
    },
    watch: {
        // 监听表单变化实时生成预览图
        form: {
            deep: true,
            handler(newVal) {
                console.log(newVal);
                this.generatePreviewImage();
            }
        }
    },
    methods: {
        // 获取经销商列表
        async getDistributorList() {
            this.loading = true;
            try {
                // 确保查询参数包含分页信息
                const params = {
                    ...this.query,
                    page: this.currentPage,
                    pageSize: this.pageSize
                };

                // 过滤掉值
                const filteredParams = Object.entries(params).reduce(
                    (acc, [key, value]) => {
                        if (
                            value !== "" &&
                            value !== null &&
                            value !== undefined
                        ) {
                            acc[key] = value;
                        }
                        return acc;
                    },
                    {}
                );

                const res = await this.$request.main.distributorList(
                    filteredParams
                );

                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.map(item => ({
                        code: item.dealer_code,
                        name: item.company_name,
                        type: item.dealer_type.toString(),
                        address: item.address,
                        phone: item.phone,
                        email: item.email,
                        startDate: item.license_start_time,
                        endDate: item.license_end_time,
                        status: item.license_status.toString(),
                        visits: item.visits,
                        createTime: item.create_time,
                        updateTime: item.update_time,
                        province_code: item.province_code,
                        city_code: item.city_code,
                        is_audit: item.is_audit,
                        area_code: item.area_code,
                        expired: item.license_expired
                    }));

                    // 更新分页数据
                    this.total = res.data.total;
                    this.currentPage = res.data.page;
                    this.pageSize = res.data.pageSize;
                }
            } catch (error) {
                console.error("获取经销商列表失败:", error);
                this.$message.error("获取数据失败");
            } finally {
                this.loading = false;
            }
        },

        // 搜索处理
        handleSearch() {
            this.currentPage = 1; // 搜索时重置为第一页
            this.query = {
                ...this.query,
                search: this.searchQuery,
                dealer_type: this.filterType,
                license_status: this.filterStatus,
                expired: this.expired,
                is_audit: this.filterAudit
            };
            this.getDistributorList();
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.currentPage = 1; // 切换每页条数时重置为第一页
            this.getDistributorList();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getDistributorList();
        },

        // 导出数据
        async exportData() {
            // 构建导出参数，包含当前的搜索条件
            const exportParams = {
                ...this.query,
                export: "excel",
                search: this.searchQuery,
                dealer_type: this.filterType,
                license_status: this.filterStatus,
                expired: this.expired
            };

            // 过滤掉空值参数
            const params = Object.entries(exportParams).reduce(
                (acc, [key, value]) => {
                    if (value !== "" && value !== null && value !== undefined) {
                        acc[key] = value;
                    }
                    return acc;
                },
                {}
            );

            // 使用 axios 发送请求并处理文件流
            const response = await this.$request.main.distributorList(params, {
                responseType: "blob" // 设置响应类型为 blob
            });

            // 创建 Blob 对象
            const blob = new Blob([response.data], {
                type: "application/vnd.ms-excel"
            });

            // 创建下载链接并触发下载
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = `经销商列表_${new Date().getTime()}.xlsx`;
            link.click();
            window.URL.revokeObjectURL(link.href);

            this.$message.success("导出成功");
        },

        initCharts() {
            this.initPieChart();
            this.initBarChart();
        },

        resizeCharts() {
            this.pieChart && this.pieChart.resize();
            this.barChart && this.barChart.resize();
        },

        initPieChart() {
            this.pieChart = echarts.init(this.$refs.pieChart);
            const option = {
                tooltip: {
                    trigger: "item",
                    formatter: "{a} <br/>{b}: {c} ({d}%)"
                },
                legend: {
                    orient: "horizontal",
                    bottom: "bottom"
                },
                series: [
                    {
                        name: "经销商类型",
                        type: "pie",
                        radius: ["50%", "70%"],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: "center"
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: "16",
                                fontWeight: "bold"
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 2, name: "战略合作商" },
                            { value: 4, name: "品牌合作商" }
                        ]
                    }
                ]
            };
            this.pieChart.setOption(option);
        },

        initBarChart() {
            this.barChart = echarts.init(this.$refs.barChart);
            const option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow"
                    }
                },
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "12%",
                    top: "10%",
                    containLabel: true
                },
                xAxis: {
                    type: "category",
                    data: [], // 初始为空数组
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: {
                    type: "value",
                    name: "经销商数量"
                },
                series: [
                    {
                        name: "经销商数量",
                        type: "bar",
                        barWidth: "40%",
                        data: [], // 初始为空数组
                        itemStyle: {
                            color: "#409EFF"
                        },
                        label: {
                            show: true,
                            position: "top",
                            distance: 10,
                            formatter: "{c}"
                        }
                    }
                ]
            };
            this.barChart.setOption(option);
        },

        async updateCharts() {
            try {
                // 获取经销商分类分布数据
                const res = await this.$request.main.getDealerCategoryStats();
                if (res.data.error_code === 0) {
                    // 新饼图数据
                    const pieData = res.data.data.map(item => ({
                        name: item.type_name,
                        value: item.count
                    }));

                    this.pieChart.setOption({
                        series: [
                            {
                                data: pieData
                            }
                        ]
                    });
                }

                // 获取省份Top10据
                const top10Res = await this.$request.main.getDealerTop10ByProvince();
                if (top10Res.data.error_code === 0) {
                    const top10Data = top10Res.data.data;

                    this.barChart.setOption({
                        xAxis: {
                            data: top10Data.map(item => item.province_name)
                        },
                        series: [
                            {
                                data: top10Data.map(item => item.dealer_count)
                            }
                        ]
                    });
                }
            } catch (error) {
                console.error("获取统计数据失败:", error);
            }
        },

        // 在表格示时转换类型显���
        getTypeLabel(type) {
            return this.typeMap[type] || type;
        },

        // 获取状态显示文本
        getStatusLabel(status) {
            return this.statusMap[status] || status;
        },

        // 获取业务员列表
        async getSalesmanList() {
            try {
                // 这里使用模拟数据

                const res = await this.$request.main.getAdminList({
                    page: 1,
                    limit: 300,
                    status: 1
                });
                this.salesmanOptions = res.data.data.list;
            } catch (error) {
                this.$message.error("获取业务员列表失败");
            }
        },

        // 修改add方法
        add() {
            this.isEdit = false;
            this.dialogVisible = true;
            this.form = {
                dealer_code: "",
                company_name: "",
                dealer_type: "",
                address: "",
                phone: "",
                email: "",
                license_time: [],
                province_code: "",
                city_code: "",
                area_code: "",
                salesman_id: ""
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.clearValidate();
                // 获取业务员列表
                this.getProvinceList();
                this.getSalesmanList();
            });
        },

        // 修改handleEdit方法
        async handleEdit(row) {
            // 检查审核状态
            if (row.is_audit === 0) {
                this.$message.warning(
                    "该经销商资料正在审核中，请耐心等待审核！"
                );
                return;
            }

            this.isEdit = true;
            this.editingRow = row;
            this.dialogVisible = true;

            // 设置基本表单数据
            this.$nextTick(async () => {
                this.form = {
                    dealer_code: row.code,
                    company_name: row.name,
                    dealer_type: parseInt(row.type),
                    address: row.address,
                    phone: row.phone,
                    email: row.email === "无" ? "" : row.email,
                    license_time: [row.startDate, row.endDate],
                    province_code: "",
                    city_code: "",
                    area_code: "",
                    salesman_id: row.salesman_id || ""
                };

                // 重置表校验
                this.$refs.form && this.$refs.form.clearValidate();

                try {
                    // 获取业务员列表
                    await this.getSalesmanList();
                    // 获取省份列表
                    await this.getProvinceList();

                    // 2. 如果有省份代码，设置并获取城市列表
                    if (row.province_code) {
                        this.form.province_code = row.province_code;
                        await this.getCityList(row.province_code);

                        // 3. 如果有城市代码，设置并获取区域列表
                        if (row.city_code) {
                            this.form.city_code = row.city_code;
                            await this.getAreaList(
                                row.province_code,
                                row.city_code
                            );

                            // 4. 如果有区域代码，设置区域
                            if (row.area_code) {
                                this.form.area_code = row.area_code;
                            }
                        }
                    } else {
                        // 如果没有省份代码，尝试从地址解析
                        const addressParts = row.address.split(" ");
                        if (addressParts.length > 0) {
                            // 查找匹配的省份
                            const province = this.provinceOptions.find(p =>
                                addressParts[0].includes(p.name)
                            );

                            if (province) {
                                this.form.province_code = province.code;
                                // 获取城市列表
                                await this.getCityList(province.code);

                                if (addressParts.length > 1) {
                                    // 查找匹配的城市
                                    const city = this.cityOptions.find(c =>
                                        addressParts[1].includes(c.name)
                                    );

                                    if (city) {
                                        this.form.city_code = city.code;
                                        // 获取区域列表
                                        await this.getAreaList(
                                            province.code,
                                            city.code
                                        );

                                        if (addressParts.length > 2) {
                                            // 查找匹配的区
                                            const area = this.areaOptions.find(
                                                a =>
                                                    addressParts[2].includes(
                                                        a.name
                                                    )
                                            );

                                            if (area) {
                                                this.form.area_code = area.code;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error("初始化表单数据失败:", error);
                    this.$message.error("初始化表单数据失败");
                }

                // 生成预览图
                await this.generatePreviewImage();
            });
        },

        // 修改submitForm方法，在提交数据时包含业务员ID
        submitForm() {
            this.$refs.form.validate(async valid => {
                if (valid) {
                    let userinfo_local = localStorage.getItem("userinfo");
                    userinfo_local = JSON.parse(userinfo_local);
                    try {
                        // 获取选中业务员的部门信息
                        const userRes = await this.$request.main.getUserNameByUid(
                            {
                                uid: this.form.salesman_id
                            }
                        );

                        if (userRes.data.error_code !== 0) {
                            throw new Error("获取业务员信息失败");
                        }

                        const userInfo = userRes.data.data;
                        console.warn(userInfo);
                        // 构建请求数据
                        const formData = {
                            ...this.form,
                            license_start_time: this.form.license_time[0],
                            license_end_time: this.form.license_time[1],
                            province_code: Number(this.form.province_code),
                            city_code: Number(this.form.city_code),
                            area_code: Number(this.form.area_code),
                            email: this.form.email || "无",
                            operator_name: userinfo_local.realname,
                            salesman_id: this.form.salesman_id,
                            dept_id: userInfo.main_department
                        };
                        delete formData.license_time;

                        // 过滤掉空字符串字段
                        const data = Object.entries(formData).reduce(
                            (acc, [key, value]) => {
                                if (value !== "") {
                                    acc[key] = value;
                                }
                                return acc;
                            },
                            {}
                        );

                        // 先保存经销商信息
                        const res = await this.$request.main[
                            this.isEdit ? "editDistributor" : "addDistributor"
                        ](data);

                        if (res.data.error_code === 0) {
                            let dealer_code = this.isEdit
                                ? ""
                                : res.data.data.dealer_code;
                            try {
                                // 使用获取到的业务员信息创建审批
                                await this.createApproval(
                                    dealer_code,
                                    userInfo,
                                    userInfo.main_department
                                );
                                this.$message.success(
                                    this.isEdit ? "编辑成功" : "添加成功"
                                );
                                this.dialogVisible = false;
                                this.getDistributorList();
                            } catch (approvalError) {
                                this.$message.warning(
                                    "经销商信息已保存，但审批流程创建失败"
                                );
                                this.dialogVisible = false;
                                this.getDistributorList();
                            }
                        }
                    } catch (error) {
                        console.error("操作失败:", error);
                        this.$message.error(
                            error.message || "保存经销商信息失败"
                        );
                    }
                }
            });
        },

        // 获取省份表
        async getProvinceList() {
            try {
                const res = await this.$request.main.getDivisions({
                    level: "province"
                });
                if (res.data.error_code === 0) {
                    this.provinceOptions = res.data.data;
                }
            } catch (error) {
                console.error("获取省份列表失败:", error);
            }
        },

        // 获取城市列表
        async getCityList(provinceCode) {
            try {
                const res = await this.$request.main.getDivisions({
                    level: "city",
                    provinceCode
                });
                if (res.data.error_code === 0) {
                    this.cityOptions = res.data.data;
                }
            } catch (error) {
                console.error("获取城市列表失败:", error);
            }
        },

        // 获取区域列表
        async getAreaList(provinceCode, cityCode) {
            try {
                const res = await this.$request.main.getDivisions({
                    level: "area",
                    provinceCode,
                    cityCode
                });
                if (res.data.error_code === 0) {
                    this.areaOptions = res.data.data;
                }
            } catch (error) {
                console.error("获取区域列表失败:", error);
            }
        },

        // 省份改变
        async handleProvinceChange(value) {
            // 清空市和区的选择
            this.form.city_code = "";
            this.form.area_code = "";
            this.cityOptions = [];
            this.areaOptions = [];
            if (value) {
                await this.getCityList(value);
            }
        },

        // 城市改变
        async handleCityChange(value) {
            // 清空区的选择
            this.form.area_code = "";
            this.areaOptions = [];
            if (value) {
                await this.getAreaList(this.form.province_code, value);
            }
        },

        // 清空城市选择
        handleCityClear() {
            this.form.city_code = "";
            this.form.area_code = "";
            this.areaOptions = [];
        },

        // 清空区域选择
        handleAreaClear() {
            this.form.area_code = "";
        },
        createApproval(code, userInfo, id) {
            let userinfo_local = localStorage.getItem("userinfo");
            // 获取省市区名称
            userinfo_local = JSON.parse(userinfo_local);
            const province =
                this.provinceOptions.find(
                    p => p.code == this.form.province_code
                )?.name || "";
            const city =
                this.cityOptions.find(c => c.code == this.form.city_code)
                    ?.name || "";
            const area =
                this.areaOptions.find(a => a.code == this.form.area_code)
                    ?.name || "";

            // 构建授权区域
            const authorizedArea = [
                province ? province : "",
                city && city != "市辖区" && city != "县" ? city : "",
                area ? area : ""
            ]
                .filter(Boolean)
                .join("");

            // 构建审批参数
            const approvalParams = {
                form_component_values: [
                    {
                        name: "经销商编号",
                        value: this.form.dealer_code
                            ? this.form.dealer_code
                            : code
                    },
                    {
                        name: "公司名称",
                        value: this.form.company_name
                    },
                    {
                        name: "经销商类型",
                        value:
                            this.form.dealer_type === 1
                                ? "战略合作商"
                                : "品牌合作商"
                    },
                    {
                        name: "联系电话",
                        value: this.form.phone
                    },
                    {
                        name: "电子邮箱",
                        value: this.form.email || "无"
                    },
                    {
                        name: "经营地址",
                        value: this.form.address
                    },
                    {
                        name: "授权区域",
                        value: authorizedArea
                    },
                    {
                        name: "授权开始日期",
                        value: this.form.license_time[0]
                    },
                    {
                        name: "授权截止日期",
                        value: this.form.license_time[1]
                    },
                    {
                        name: "中台发起者",
                        value: [
                            {
                                userid: userinfo_local.dt_userid,
                                name: userinfo_local.realname
                            }
                        ]
                    }
                ],
                process_code: "Zw332D4vp6CLAfKSRdaBpm7Dt9G1kCX4GLVnAh",
                dept_id: id || "",
                originator_user_id: this.form.salesman_id
            };
            return this.$request.main
                .createApproval(approvalParams)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("审批流程已创建");
                    } else {
                        this.$message.error("创建审批流程失败");
                    }
                })
                .catch(error => {
                    console.error("创建审批失败:", error);
                    this.$message.error("创建审批流程失败");
                });
        },
        // 显示二维码
        async showQrcode(row) {
            const res = await this.$request.main.getQrcode({
                certid: row.code
            });

            if (res.data.error_code === 0) {
                this.qrcodeUrl = res.data.data;
                this.qrcodeVisible = true;
            }
        },

        // 处理状态切换
        async handleStatusChange(row) {
            // 确认弹窗
            try {
                await this.$confirm(
                    `确定要${row.status === "1" ? "禁用" : "启用"}该经销商吗？`,
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: row.status === "1" ? "warning" : "success"
                    }
                );

                const newStatus = row.status === "1" ? 0 : 1;
                let userinfo = localStorage.getItem("userinfo");
                // let operator_id = Cookies.get("uid");
                userinfo = JSON.parse(userinfo);

                const data = {
                    dealer_code: row.code,
                    license_status: newStatus,
                    operator_name: userinfo.realname
                };

                const res = await this.$request.main.changeDistributorStatus(
                    data
                );

                if (res.data.error_code === 0) {
                    this.$message.success("操作成功");
                    // 更新本地数据
                    row.status = newStatus.toString();
                }
            } catch (error) {
                // 如果用户点击取消，不会抛出错误
                if (error !== "cancel") {
                    console.error("状态切换失败:", error);
                    this.$message.error("操作失败");
                }
            }
        },

        // 修改 openNewWindow 方法
        openNewWindow(code) {
            // 跳转到证书验证页面
            const url = `https://cert.mulando.cn/?certid=${code}`;
            window.open(url, "_blank");
        },

        // 添加生成预览图方法
        async generatePreviewImage() {
            if (!this.form.company_name) return;

            try {
                // 获取省市区名称
                const province =
                    this.provinceOptions.find(
                        p => p.code == this.form.province_code
                    )?.name || "";
                const city =
                    this.cityOptions.find(c => c.code == this.form.city_code)
                        ?.name || "";
                const area =
                    this.areaOptions.find(a => a.code == this.form.area_code)
                        ?.name || "";

                // 构建授权区域
                const authorizedArea = [
                    province ? province : "",
                    city && city != "市辖区" && city != "县" ? city : "",
                    area ? area : ""
                ]
                    .filter(Boolean)
                    .join("");

                // 获取开始日期和结束日期
                const startDate = this.form.license_time?.[0];
                const endDate = this.form.license_time?.[1];
                if (!startDate) return;

                // 判断是否过期
                const currentDate = new Date();
                const endDateTime = new Date(endDate);
                const isExpired = currentDate > endDateTime;

                // 转换日期格式
                const formatDate = dateStr => {
                    const date = new Date(dateStr);
                    const months = [
                        "January",
                        "February",
                        "March",
                        "April",
                        "May",
                        "June",
                        "July",
                        "August",
                        "September",
                        "October",
                        "November",
                        "December"
                    ];
                    return `${
                        months[date.getMonth()]
                    } ${date.getDate()}, ${date.getFullYear()}`;
                };

                const params = {
                    companyName: this.form.company_name,
                    authorizedArea,
                    authDateChinese: "授权时间:" + startDate + " 到 " + endDate,
                    authDateEnglish: `From ${formatDate(
                        startDate
                    )} to ${formatDate(endDate)}`,
                    yearMonth:
                        startDate.substring(0, 7).replace("-", "年") + "月",
                    authType: this.form.dealer_type,
                    isExpired // 根据时间比较结果设置
                };

                const res = await this.$request.main.generateCertImage(params);
                if (res.data.error_code === 0) {
                    this.previewImageUrl = res.data.data.data;
                }
            } catch (error) {
                console.error("生成预览图片失败:", error);
            }
        },

        // 添加下载二维码方法
        downloadQRCode() {
            if (!this.qrcodeUrl) return;

            const link = document.createElement("a");
            link.href = this.qrcodeUrl;
            link.download = `qrcode_${new Date().getTime()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        // 获取审核状态标签
        getAuditStatusLabel(status) {
            return this.auditStatusMap[status]
                ? this.auditStatusMap[status]
                : "未知状态";
        },

        // 获取审核状态类型
        getAuditStatusType(status) {
            const typeMap = {
                0: "warning", // 待审核
                1: "success", // 已审核
                2: "danger" // 未通过
            };
            return typeMap[status] || "info";
        }
    }
};
</script>

<style lang="scss" scoped>
.distributor-list {
    padding: 20px;

    .search-area {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .search {
            margin-right: 10px;
        }
        .filter-items {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .charts-container {
        margin: 20px 0;
        display: flex;
        gap: 20px;

        .chart-card {
            flex: 1;

            .chart {
                height: 300px;
            }
        }
    }

    // 添加表单项的间距
    /deep/ .el-form-item {
        margin-bottom: 20px !important;
    }

    .code-cell {
        display: flex;
        align-items: center;

        .el-button {
            padding: 0;
            margin-left: 8px;

            i {
                margin-right: 4px;
            }

            &:hover {
                color: #409eff;
            }
        }
    }

    .dialog-content {
        display: flex;
        gap: 20px;

        .form-section {
            flex: 1;
            min-width: 600px;
        }

        .preview-section {
            width: 500px;

            .preview-title {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 16px;
                color: #333;
            }

            .preview-container {
                background: #f5f7fa;
                border-radius: 4px;
                padding: 16px;
                min-height: 400px;
                display: flex;
                align-items: center;
                justify-content: center;

                .preview-image {
                    width: 100%;
                    height: auto;
                    object-fit: contain;
                }

                .no-preview {
                    color: #909399;
                    font-size: 14px;
                }
            }
        }
    }

    // 添加二维码弹窗样式
    :deep(.qrcode-dialog) {
        .el-dialog__body {
            padding: 20px 30px;
        }
    }

    .qrcode-content {
        text-align: center;

        .qrcode-wrapper {
            background: #f5f7fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;

            .qrcode-image {
                max-width: 200px;
                height: auto;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                border-radius: 4px;
                transition: transform 0.3s ease;

                &:hover {
                    transform: scale(1.02);
                }
            }

            .qrcode-loading {
                color: #909399;
                font-size: 14px;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;

                i {
                    font-size: 24px;
                }
            }
        }

        .qrcode-tips {
            p {
                color: #606266;
                margin-bottom: 15px;
                font-size: 14px;
            }

            .el-button {
                i {
                    margin-right: 5px;
                }
            }
        }
    }
}
</style>
