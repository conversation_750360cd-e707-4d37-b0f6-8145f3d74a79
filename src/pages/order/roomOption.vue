<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
             :inline-message="true"
        >
           
            <el-form-item
                    label="房间名称"
                    :label-width="formLabelWidth"
                    prop="room_id"
                >
                <el-select
                    v-model="form.room_id"
                    placeholder="必填"
                    style="width: 200px; margin-right: 10px;"
                >
                    <el-option
                    v-for="item in roomList"
                    :key="item.id"
                    :label="item.room_name"
                    :value="item.id">
                    </el-option>
                </el-select>
                </el-form-item>
                <el-form-item
                    label="入住时间"
                    :label-width="formLabelWidth"
                    prop="check_in_time"
                >
                    <el-date-picker
                    :picker-options="pickerOptions"
                        v-model="chooseTime"
                         type="daterange"
                        range-separator="-"
                        start-placeholder="入住开始日期"
                        end-placeholder="入住离开日期"
                        align="right"
                        @change="changeTime"
                         format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item  :label-width="formLabelWidth" label="入住人" prop="customer_name">
                <el-input
                class="w-normal"
                v-model="form.customer_name"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item  :label-width="formLabelWidth" label="入住人电话" prop="customer_phone">
                <el-input
                class="w-normal"
                v-model="form.customer_phone"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item  :label-width="formLabelWidth" label="入住人数" prop="people_number">
                <el-input
                class="w-normal"
                v-model="form.people_number"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item  :label-width="formLabelWidth" label="备注" prop="remark">
                <el-input
                class="w-normal"
                type="textarea"
                :rows="2"
                v-model="form.remark"
                  placeholder="请输入"
                  clearable
                ></el-input>
            </el-form-item>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>

export default {
    props: {
        parentObj: Object,
    },
    data() {
       
        return {
          
            form: {
                room_id: "",
                check_in_time: "",
                customer_name: "",
                customer_phone: "",
                check_out_time:"",
                people_number:"",
                remark:""
            },
            pickerOptions: {
                disabledDate(v) {
                return v.getTime() < new Date().getTime() - 86400000;//  - 86400000是否包括当天
                }
            },
            roomList:[],
            chooseTime:[],
            formRules: {
                room_id: [
                    {
                        required: true,
                        message: "请输入房间名",
                        trigger: "blur"
                    }
                ],
                check_in_time: [
                    {
                        required: true,
                        message: "请选择入住时间",
                        trigger: "blur"
                    }
                ],
                customer_name: [
                    {
                        required: true,
                        message: "请输入入住人",
                        trigger: "blur"
                    }
                ],
                customer_phone: [
                    {
                        required: true,
                        message: "请输入入住人电话",
                        trigger: "blur"
                    }
                ],
                people_number: [
                    {
                        required: true,
                        message: "请输入入住人数",
                        trigger: "blur"
                    }
                ],
            },
            formLabelWidth: "100px",
            cloumOptions:[],
            loading: false,
            isEdit:false,
        };
    },
    mounted() {
        this.fetchRoomList();     
    },
    beforeDestroy() {
        this.isEdit = false;
    },
    methods: {
       
       //房间列表
       async fetchRoomList(){
      let res = await this.$request.main.getHotelRoomsList(this.query);
            
            if (res.data.error_code == 0) {
                this.roomList = res.data.data.list;
            }
        },
        changeTime() {
            this.form.check_in_time = this.chooseTime ? this.chooseTime[0] : "";
            this.form.check_out_time = this.chooseTime ? this.chooseTime[1] : "";  
        },
       
        closeDiog() {
            this.$emit("close");
           
        },
        
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                   
                    this.$request.main.predetermineRoom(
                        this.form
                    ).then(result => {
                        if (result.data.error_code == 0) {
                            this.closeDiog();
                            this.parentObj.getbookingRecordsListReq();
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
