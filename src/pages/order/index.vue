<template>
       <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.room_name"
                    placeholder="房间名称"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.customer_name"
                    placeholder="入住人名称"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.customer_phone"
                    placeholder="入住人电话"
                ></el-input>
                <el-date-picker
                        v-model="form.check_in_time"
                        type="date"
                        placeholder="入住时间"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="m-r-10"
                         size="mini"
                    >
                </el-date-picker>
                <el-date-picker
                        v-model="form.check_out_time"
                        type="date"
                        placeholder="离开时间"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="m-r-10"
                          size="mini"
                    >
                </el-date-picker>
                <!-- <el-date-picker
                        v-model="form.voucher_date"
                        type="date"
                        placeholder="申请时间"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="m-r-10"
                    >
                </el-date-picker> -->
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.applicant"
                    placeholder="申请人"
                ></el-input>
               
                <el-button type="primary" size="mini" @click="search"
                        >查询</el-button
                    >
                <el-button type="danger" @click="predetermineDialogStatus = true"  size="mini">订房</el-button>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card
             :body-style="{padding: '10px'}"
                class="card"
                shadow="hover"
                v-for="(item) in tableData"
                :key="item.id"
            >
                <div class="card-title">
                    <div>
                        {{ item.hotel_name }}-{{ item.room_name }}
                    </div>
                    <div>
                        <span style="margin-right: 20px;  color: #ff0b1c;">{{ item.extended_status }}</span>
                        <span :style="{color:colorArr[item.status]}">{{item.status | statusText }}</span>
                    </div>
                </div>
                <div class="order-main">
                    <div style="display: flex; ">
                        <div>
                            <div>
                            <b>申请人：</b
                            >
                            <div>{{item.applicant }}</div>
                            </div>
                            <div>
                            <b>入住人电话：</b
                            >
                            <div>{{item.customer_phone }}</div>
                            </div>
                            <div>
                            <b>申请类型：</b
                            ><div>{{item.payment_method ==1 ?'到店支付': `兔头${item.rabbit}` }}</div>
                            </div>
                        </div>

                        <div>
                            <div>
                            <b>入住人：</b>
                             <div>{{item.customer_name }}</div>
                            </div>
                            <div>
                            <b>入住时间：</b>
                            <div>{{item.check_in_time }}</div>
                            </div>
                            <div>
                            <b>离开时间：</b>
                            <div>{{item.check_out_time }}</div>
                            </div>
                            <div>
                            <b>入住天数：</b>
                            <div>{{item.check_in_days }}</div>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column-reverse; ">
                        
                        <div v-if="item.status == 2" style="display: flex; flex-direction: row-reverse;width: 100%; ">
                            <el-button style="margin-left: 10px; background-color: #f2571f; color: #fff;"  size="mini" @click="changeOrContinuedRoom(true, item)"
                                >换房</el-button
                            >
                            <el-button  style="margin-left: 10px; background-color: #f2571f; color: #fff;" size="mini" @click="changeOrContinuedRoom(false, item)"
                                >续房</el-button
                            >
                            <el-button  style="margin-left: 10px; background-color: #f2571f; color: #fff;"  size="mini" @click="btnOptione(item.id, 2)"
                                >退房</el-button
                            >
                        </div>
                        <div v-else-if="item.status == 1"  style="display: flex; flex-direction: row-reverse;width: 100%; ">
                            <el-button style="margin-left: 10px; background-color: #f2571f; color: #fff;"  size="mini" @click="btnOptione(item.id, 1)"
                                >确认入住</el-button
                            >
                        </div>
                    </div>
                   
                </div>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
       
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            :visible.sync="predetermineDialogStatus"
            title="预定房间"
            custom-class="dialogwid"
            width="50%"
            height="100%"
        >
            <RoomOption
                v-if="predetermineDialogStatus"
                :parentObj="this"
                @close="close"
            ></RoomOption>
        </el-dialog>

        <el-dialog
            :visible.sync="changeDialogVisble"
            :title="isChangeRoom ? '换房' : '续房'"
            custom-class="dialogwid"
            width="50%"
            height="100%"
        >
            <ChangeRoom
                v-if="changeDialogVisble"
                :parentObj="this"
                :isChange="isChangeRoom"
                :itemData="rowData"
                @closeChange="closeChange"
            ></ChangeRoom>
        </el-dialog>
    </div>
</template>
<script>
import ChangeRoom from "./changeRoom.vue";
import RoomOption from "./roomOption.vue";
export default {
    components: { RoomOption, ChangeRoom},
    data() {
        return {
            rowData: {},
            tableData: [],
           colorArr:['#f2571f', '#f2571f', '#f2571f', '#ff0b1c', '#333333'],
           predetermineDialogStatus: false,
           changeDialogVisble:false,
            form: {
                page: 1,
                limit: 10,
                room_name:"",
                customer_name:"",
                customer_phone:"",
                check_in_time:"",
                check_out_time:"",
                applicant:"",
                // hotel_id:"",
            },
            total: 0,
            hotelOptions:[],
            isChangeRoom: false
        };
    },
    filters: {
        // 0-待支付，1-待入住，2-入住中，3-已完成，4-已取消
        statusText(input) {
            return (
                {
                    0: "待支付",
                    1: "待入住",
                    2: "入住中",
                    3: "已完成",
                    4: "已取消",
                }[input] || ""
            );
        },
    },
    mounted() {
        this.getbookingRecordsListReq();
    //    this.getHotelListReq();
    },
    methods: {
        //banner
        async getbookingRecordsListReq() {
            let res = await this.$request.main.getbookingRecordsList(this.form);
            console.log("banner列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async getHotelListReq() {
            let res = await this.$request.main.getHotelList({page:1, limit:999});
           
            if (res.data.error_code == 0) {
                this.hotelOptions = res.data.data.list;
               
            }
        },
       
       search(){
        this.getbookingRecordsListReq();
       },
       changeOrContinuedRoom(isChange, item){
        console.log(isChange, item);
        
        this.rowData = item;
        this.isChangeRoom = isChange;
        this.changeDialogVisble = true;
       },
        btnOptione(id, type){
            var tipStr;
            if(type==1){
                tipStr = "是否确认入住？";
            } else {
                tipStr = "是否确认退房？";
            }
            this.$confirm(tipStr, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$request.main.bookingRecordOperate({ id: id, operate_type:type }).then((res) => {
                if (res.data.error_code === 0) {
                    this.$message.success("操作成功");
                    this.getbookingRecordsListReq();
                }
                });
            });
            
        },
      
        close() {
            this.predetermineDialogStatus = false;
        },
        closeChange() {
            this.changeDialogVisble = false;
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getbookingRecordsListReq();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getbookingRecordsListReq();
        }
    },

};
</script>
<style scoped lang="scss">

/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .flex-layout {
        display: flex;
    }
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .overflow {
            width: 100%;
            overflow: hidden;
            color: #909399;
            cursor: pointer;
            line-height: 2;
            text-overflow: ellipsis;
            font-weight: 500;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                font-size: 14px;
                font-weight: 800;
                display: flex;
                align-items: center;
                justify-content: space-between;
               margin-bottom: 10px;
                .m-l-8 {
                    margin-left: 10px;
                }
              
            }
        }

        .order-main {
            display: flex;
            justify-content: space-between;
            & >div{
                & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 100px;
                margin-right: 50px;

                color: #333;

                & > div {
                    display: flex;
                    align-items: center;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }
            }
            } 
        }
    }
}
</style>
