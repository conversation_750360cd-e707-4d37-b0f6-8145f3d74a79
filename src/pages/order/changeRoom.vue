<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            :inline-message="true"
        >
           
            <el-form-item
                    label="房间名称"
                    :label-width="formLabelWidth"
                    prop="room_id"
                >
                <el-select
                    v-model="form.room_id"
                    placeholder="必填"
                    style="width: 200px; margin-right: 10px;"
                >
                    <el-option
                    v-for="item in roomList"
                    :key="item.id"
                    :label="item.room_name"
                    :value="item.id">
                    </el-option>
                </el-select>
                </el-form-item>
                <el-form-item
                    label="入住时间"
                    :label-width="formLabelWidth"
                    prop="check_in_time"
                >
                <el-date-picker
                :disabled="isChange"
                        v-model="form.check_in_time"
                        type="date"
                        placeholder="入住时间"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="m-r-10"
                         size="mini"
                         :picker-options="pickerOptions"
                    >
                </el-date-picker>
                </el-form-item>
                <el-form-item
                    label="离开时间"
                    :label-width="formLabelWidth"
                    prop="check_out_time"
                >
                <el-date-picker
                :disabled="isChange"
                        v-model="form.check_out_time"
                        type="date"
                        placeholder="离开时间"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="m-r-10"
                          size="mini"
                          :picker-options="pickerOptions"
                    >
                </el-date-picker>
                </el-form-item>
                <el-form-item  :label-width="formLabelWidth" label="申请类型" prop="payment_method">
                    <el-select
                    v-model="form.payment_method"
                    placeholder="请选择"
                    :disabled="isChange"
                >
                    <el-option
                    v-for="item in patMented"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>

export default {
    props: {
        parentObj: Object,
        isChange:Boolean,
        itemData:Object,
    },
    data() {
       
        return {
          
            form: {
                room_id: "",
                check_in_time: "",
                check_out_time:"",
                payment_method:1,
                id:""
            },
            pickerOptions: {
                disabledDate(v) {
                return v.getTime() < new Date().getTime() - 86400000;//  - 86400000是否包括当天
                }
            },
            roomList:[],
            patMented:[{id:0, name:'兔头'}, {id:1, name:'到店支付'}],
            formRules: {
                room_id: [
                    {
                        required: true,
                        message: "请输入房间名",
                        trigger: "blur"
                    }
                ],
                check_in_time: [
                    {
                        required: true,
                        message: "请选择入住时间",
                        trigger: "blur"
                    }
                ],
                payment_method: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "blur"
                    }
                ],
                check_out_time: [
                    {
                        required: true,
                        message: "请选择离开时间",
                        trigger: "blur"
                    }
                ],
            },
            formLabelWidth: "100px",
            cloumOptions:[],
            loading: false,
            isEdit:false,
        };
    },
    mounted() {
        this.fetchRoomList();     
        console.log(1111113344);
        this.form.id = this.itemData.id;
        if(this.isChange){
            this.form.check_in_time = this.itemData.check_in_time.split(' ')[0];
            this.form.check_out_time = this.itemData.check_out_time.split(' ')[0];
            this.form.payment_method = this.itemData.payment_method;
        } else{
            this.form.room_id = this.itemData.room_id;
            this.form.payment_method = this.itemData.payment_method;
        }
    },
    beforeDestroy() {
        this.isEdit = false;
    },
    methods: {
       
       //房间列表
       async fetchRoomList(){
      let res = await this.$request.main.getHotelRoomsList(this.query);
            
            if (res.data.error_code == 0) {
                this.roomList = res.data.data.list;
            }
        },
        disabledDate(time) {
            return time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天
        },
        closeDiog() {
            this.$emit("closeChange");
           
        },
        
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    var isEditName = "";
                if (this.isChange) {
                    isEditName = "changeRoom";
                } else {
                    isEditName = "extendRoom";
                }
                    this.$request.main[isEditName](
                        this.form
                    ).then(result => {
                        if (result.data.error_code == 0) {
                            this.closeDiog();
                            this.parentObj.getbookingRecordsListReq();
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
