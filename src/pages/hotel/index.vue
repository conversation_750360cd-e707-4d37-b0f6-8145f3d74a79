<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                        placeholder="酒店名称"
                        @keyup.enter.native="search"
                        v-model="query.name"
                        clearable
                        style="margin-right: 10px; width: 180px;"
                    />
                    <el-button
                    type="primary"
                    size="normal"
                    @click="search"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="normal"
                    @click="updateBulleinVisible = true"
                    >新增</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card
                class="card"
                shadow="hover"
                v-for="(item, index) in tableData"
                :key="index"
            >
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div>
                    <b>酒店名称：</b>
                   <span>{{ item.name }}</span>
                    </div>
                    <div>
                    <b>酒店英文名称：</b
                    > <span>{{ item.en_name }}</span>
                    </div>
                    <div>
                    <b>酒店地址：</b
                    ><span>{{ item.address }}</span>
                    </div>
                    <div>
                    <b>酒店电话：</b
                    ><span>{{ item.phone }}</span>
                    </div>
                </div>
                <div style="display: flex;">
                    <div style="display: flex; align-items: center; margin-right: 10px;"><span style="color:#f2571f; padding-right: 3px;">{{item.room_count}}</span>间客房</div>
                    <el-button type="primary" size="mini" @click="editBullein(item)"
                        >编辑</el-button
                    >
                </div>
            </div>
           
            </el-card>
        </div>
        <el-empty v-else></el-empty>
  
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
       
        <div>
            <el-dialog
                title="基础信息"
                :visible.sync="updateBulleinVisible"
                width="50%"
                :close-on-click-modal="false"
            >
                <UpdatePro
                    @closeBulleinDialog="closeBulleinDialog"
                    v-if="updateBulleinVisible"
                    ref="updatebullein"
                ></UpdatePro>
                <span slot="footer" style="display:flex;justify-content:center">
                    <el-button @click="updateBulleinVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateBullein"
                        >确定</el-button
                    >
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>

import UpdatePro from "./updatePro.vue";

export default {
    components: { UpdatePro},

    data() {
        return {
            rowData: {},
            editId: "",
            tableData: [],
            updateBulleinVisible:false,
            query: {
                page: 1,
                limit: 10,
                name: ""
            },
            total: 0,
            isEdit: false,
            optioneTitle:"",
        };
    },
    mounted() {
       
        this.getHotelListReq();
       
    },
    methods: {
       
        //试卷列表
        async getHotelListReq() {
            let res = await this.$request.main.getHotelList(this.query);
            console.log("列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
       
        //查询
        search() {
            this.query.page = 1;
            this.getHotelListReq();
        },
        closeBulleinDialog() {
            this.updateBulleinVisible = false;
            this.getHotelListReq();
            this.$message({
                type: "success",
                message: "操作成功"
            });
        },
      
         //关闭商品管理弹框
         closeGoodsListVisible() {
            this.goodsListVisible = false;
            this.getHotelListReq();
        },
        comfirmUpdateBullein() {
            this.$nextTick(() => {
                this.$refs.updatebullein.updateBullein();
            });
        },
        editBullein(row) {
            this.updateBulleinVisible = true;
            this.$nextTick(() => {
                this.$refs.updatebullein.editRow(row);
            });
        },
        
        showChaptersList(row) {
            console.log("1000---", row);
            this.$nextTick(() => {
                this.$refs.relatedQuestionsref.openForm(row);
            });
        },
        async deleteData(row) {
           
            let data = {
                id: row.id,
            };
            let res = await this.$request.main.deleteExam(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getHotelListReq();
            }
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getHotelListReq();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.getHotelListReq();
        }
    },

    filters: {
    },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

    }
}
</style>
