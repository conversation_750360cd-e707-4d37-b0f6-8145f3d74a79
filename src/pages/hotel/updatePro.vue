<template>
    <div>
        <el-form :model="form" ref="updateBulleinForm" :rules="formRules" label-width="120px"
            :inline="false" size="normal" :inline-message="true">
            <el-form-item label="酒店名称" prop="name">
                <el-input v-model="form.name" size="small"  placeholder="请输入酒店名称" style="width: 240px;">
                </el-input>
            </el-form-item>
            <el-form-item label="酒店英文名称" prop="en_name">
                <el-input v-model="form.en_name" size="small"  placeholder="请输入英文名称" style="width: 240px;">
                </el-input>
            </el-form-item>
            <el-form-item label="酒店地址" prop="address">
                <el-input v-model="form.address" size="small"  placeholder="请输入酒店地址" style="width: 240px;">
                </el-input>
            </el-form-item>
            <el-form-item label="酒店电话" prop="phone">
                <el-input v-model="form.phone" size="small"  placeholder="请输入酒店电话" style="width: 240px;">
                </el-input>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>

export default {
   
    components: {
        
    },
    data() {
        
        return {
            form: {
                name: "",
                id: "",
                en_name:"",
                address:"",
                phone:""
            },
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入酒店名称",
                        trigger: "blur"
                    }
                ],
                en_name: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur"
                    }
                ],
                address: [
                    {
                        required: true,
                        message: "请输入酒店地址",
                        trigger: "blur"
                    }
                ],
                phone: [
                    {
                        required: true,
                        message: "请输入酒店电话",
                        trigger: "blur"
                    }
                ],
                
            },
            isEdit: false
        };
    },

    mounted() {

    },

    methods: {

        editRow(row) {
            console.log('022222', row);
            this.form.name = row.name;
            this.form.id = row.id;
            this.form.en_name = row.en_name;
            this.form.address = row.address;
            this.form.phone = row.phone;
            this.isEdit = true;
        },

        updateBullein() {
            this.$refs.updateBulleinForm.validate(valid => {
                if (valid) {
                    let isEditName = "";
                    if (this.isEdit) {
                        isEditName = "editHotel";
                    } else {
                        isEditName = "addHotel";
                    }
                    this.$request.main[isEditName](
                        this.form
                    ).then(result => {
                        if (result.data.error_code == 0) {
                            this.$emit("closeBulleinDialog");
                        }
                    });
                } else {
                    return false;
                }
            });
        }
    },
    beforeDestroy() {
        this.isEdit = false;
    }
};
</script>
