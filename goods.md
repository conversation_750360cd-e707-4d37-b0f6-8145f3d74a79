# 商品列表接口（后台）

## 接口描述
获取商品列表，支持分页和多种筛选条件

## 请求信息
- **URL**: `/mulandoGreateDestiny/v1/admin/goods/list`
- **Method**: `GET`
- **认证**: 需要管理员权限（Global + Admin中间件）

## 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int64 | 否 | 1 | 页码 |
| limit | int64 | 否 | 10 | 每页数量 |
| title | string | 否 | - | 商品标题筛选 |
| type | int64 | 否 | - | 商品类型筛选 1=普通商品,2=抽奖商品|
| onsale_status | int64 | 否 | - | 上架状态筛选(2=上架,3=下架) |
| label_id | int64 | 否 | - | 标签ID筛选 |

## 响应示例
**成功响应**:
```json
{
    "error_code": 0,
    "error_msg": "OK",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "精美礼品盒",
                "type": 1,
                "items_info": "[{\"name\":\"商品A\",\"short_code\":\"A001\",\"num\":1}]",
                "price": 99.99,
                "inventory": 100,
                "erp_amount": 80.00,
                "cashback_amount": 5.00,
                "deductible_amount": 10.00,
                "product_img": "https://example.com/product1.jpg,https://example.com/product2.jpg",
                "avatar_image": "https://example.com/avatar1.jpg",
                "detail": "商品详细描述信息",
                "onsale_status": 2,
                "sort": 1000,
                "sales_user_num": 50,
                "pv": 1001,
                "delete_time": 0,
                "vh_uid": 1,
                "vh_vos_name": "admin",
                "created_time": "2023-01-01 12:00:00",
                "update_time": "2023-01-01 12:00:00",
                "labels": [
                    {
                        "label_id": 1,
                        "name": "热门"
                    }
                ]
            }
        ],
        "total": 100
    }
}
```

**失败响应**:
```json
{
    "error_code": 100001,
    "error_msg": "服务器错误"
}
```

## 响应参数说明
| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 商品列表 |
| total | int64 | 总数量 |

### 商品信息字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 商品ID |
| title | string | 商品标题 |
| type | int64 | 商品类型:1=普通商品,2=抽奖商品 |
| items_info | string | 商品详情JSON字符串 |
| price | float64 | 商品价格 |
| inventory | int64 | 库存数量 |
| erp_amount | float64 | ERP金额 |
| cashback_amount | float64 | 返现金额 |
| deductible_amount | float64 | 可抵扣金额 |
| product_img | string | 商品图片(多图用逗号分隔) |
| avatar_image | string | 商品头像图 |
| detail | string | 商品详细描述 |
| onsale_status | int64 | 上架状态(2=上架,3=下架) |
| sort | int64 | 排序值 |
| sales_user_num | int64 | 购买人数 |
| pv | int64 | 浏览量 |
| delete_time | int64 | 删除时间(0=未删除) |
| vh_uid | int64 | 操作用户ID |
| vh_vos_name | string | VOS用户姓名 |
| created_time | string | 创建时间 |
| update_time | string | 更新时间 |
| labels | array | 商品标签列表 |

## 调用示例
```bash
# 获取所有商品
curl -X GET "http://localhost:8888/mulandoGreateDestiny/v1/admin/goods/list?page=1&limit=10" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4="

# 按标题搜索
curl -X GET "http://localhost:8888/mulandoGreateDestiny/v1/admin/goods/list?page=1&limit=10&title=礼品" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4="

# 按状态筛选
curl -X GET "http://localhost:8888/mulandoGreateDestiny/v1/admin/goods/list?page=1&limit=10&onsale_status=2" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4="

# 按标签筛选
curl -X GET "http://localhost:8888/mulandoGreateDestiny/v1/admin/goods/list?page=1&limit=10&label_id=1" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4="
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 100001 | 服务器错误 |
| 100002 | 参数错误 |
| 100003 | 用户不存在 |
| 100005 | 数据库错误 |

## 注意事项
1. 只返回未删除的商品(delete_time=0)
2. 支持多种筛选条件的组合使用
3. 标题搜索支持模糊匹配
4. 返回结果按sort字段排序
5. 包含商品的标签信息
