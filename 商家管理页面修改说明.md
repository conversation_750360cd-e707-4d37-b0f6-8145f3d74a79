# 商家管理页面修改说明

## 修改概述

已成功将商家管理页面从使用模拟数据改为使用真实的API接口，实现了完整的商家列表获取和审核功能。

## 主要修改内容

### 1. API集成
- **导入API服务**: 引入了 `@/services/main/main.js` 中的API服务
- **商家列表获取**: 使用 `getMerchantList` 接口获取商家数据
- **商家审核**: 使用 `merchantReview` 接口进行商家审核（通过/驳回）

### 2. 数据结构调整
- **状态映射**: 将API返回的状态码（2=待审核，3=已通过，4=已驳回）映射到组件状态
- **数据转换**: 将API返回的数据格式转换为组件需要的格式
- **分页处理**: 改为服务端分页，使用API返回的total值

### 3. 功能增强
- **搜索功能**: 支持关键词搜索（手机号、公司名称、联系人、地址）
- **排序功能**: 支持按公司名称排序（升序/降序）
- **审核操作**: 
  - 待审核状态：显示"通过"和"驳回"按钮
  - 已驳回状态：显示"查看原因"按钮，可查看驳回原因
  - 已通过状态：显示"无操作"
- **驳回原因**: 驳回时必须填写驳回原因

### 4. 用户体验优化
- **加载状态**: 在数据加载时显示loading状态
- **错误处理**: 添加了完善的错误处理和用户提示
- **确认对话框**: 通过审核时显示确认对话框
- **驳回原因输入**: 驳回时要求输入驳回原因（必填，最多200字符）

## API接口说明

### getMerchantList 接口
**请求参数:**
- `keyword`: 搜索关键词（可选）
- `order_column`: 排序列，支持 "company_name"
- `order_by`: 排序方式，"asc" 或 "desc"
- `page`: 页码，默认1
- `limit`: 每页数量，默认10

**返回数据结构:**
```json
{
  "data": {
    "list": [
      {
        "id": 1,
        "phone": "手机号",
        "company_name": "公司名称",
        "contact_name": "联系人",
        "unified_social_code": "税号",
        "business_license_image": "营业执照图片URL",
        "province_name": "省",
        "city_name": "市", 
        "district_name": "区",
        "address": "详细地址",
        "create_time": "注册时间",
        "status": "状态(2待审核,3通过,4驳回)",
        "reason": "驳回原因",
        "last_login_time": "最后登录时间"
      }
    ],
    "total": "总数量"
  }
}
```

### merchantReview 接口
**请求参数:**
```json
{
  "id": "商家ID",
  "is_pass": "是否通过(true/false)",
  "reason": "驳回原因(不通过时必填)"
}
```

## 文件修改位置
- 文件路径: `src/pages/mulandoDraw/merchantManagement/index.vue`
- 主要修改: 
  - 导入API服务
  - 更新数据结构和状态映射
  - 重写loadMerchants方法
  - 添加handleApprove方法
  - 更新confirmReject方法
  - 修改分页和搜索逻辑
  - 更新UI组件（操作列、驳回对话框）

## 测试建议
1. 测试商家列表加载功能
2. 测试搜索功能（关键词搜索）
3. 测试排序功能（按公司名称）
4. 测试分页功能
5. 测试审核通过功能
6. 测试审核驳回功能（包括驳回原因验证）
7. 测试错误处理（网络错误、API错误等）

## 注意事项
- 确保API接口已正确配置和可访问
- 确保用户有相应的权限进行商家审核操作
- 驳回原因为必填项，最多200个字符
- 所有操作都会重新加载数据以确保数据一致性
