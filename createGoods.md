# 创建商品接口（后台）

## 接口描述
创建新的商品

## 请求信息
- **URL**: `/mulandoGreateDestiny/v1/admin/goods/create`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **认证**: 需要管理员权限（Global + Admin中间件）

## 请求参数
```json
{
    "title": "精美礼品盒",
    "type": 1,
    "items_info": "[{\"name\":\"商品A\",\"short_code\":\"A001\",\"num\":1}]",
    "price": 99.99,
    "inventory": 100,
    "erp_amount": 80.00,
    "cashback_amount": 5.00,
    "deductible_amount": 10.00,
    "product_img": "https://example.com/product1.jpg,https://example.com/product2.jpg",
    "avatar_image": "https://example.com/avatar1.jpg",
    "detail": "商品详细描述信息",
    "onsale_status": 3,
    "labels": [
        {
            "label_id": 1,
            "name": "热门"
        },
        {
            "label_id": 2,
            "name": "新品"
        }
    ]
}
```

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| title | string | 是 | - | 商品标题 |
| type | int64 | 是 | - | 商品类型:1=普通商品,2=抽奖商品 |
| items_info | string | 是 | - | 商品详情JSON字符串 |
| price | float64 | 是 | - | 商品价格(最小为0) |
| inventory | int64 | 是 | - | 库存数量(最小为0) |
| erp_amount | float64 | 否 | 0 | ERP金额(最小为0) |
| cashback_amount | float64 | 否 | 0 | 返现金额(最小为0) |
| deductible_amount | float64 | 否 | 0 | 可抵扣金额(最小为0) |
| product_img | string | 否 | - | 商品图片(多图用逗号分隔) |
| avatar_image | string | 否 | - | 商品头像图 |
| detail | string | 否 | - | 商品详细描述 |
| onsale_status | int64 | 否 | 3 | 上架状态(2=上架,3=下架) |
| labels | array | 否 | - | 商品标签列表 |

### 标签信息字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| label_id | int64 | 是 | 标签ID |
| name | string | 是 | 标签名称 |

## 响应示例
**成功响应**:
```json
{
    "error_code": 0,
    "error_msg": "OK"
}
```

**失败响应**:
```json
{
    "error_code": 100002,
    "error_msg": "参数错误: 商品标题不能为空"
}
```

## 业务规则
1. **标题唯一性**: 商品标题不能重复
2. **价格限制**: 价格必须大于等于0
3. **库存限制**: 库存必须大于等于0
4. **图片格式**: 多图用逗号分隔
5. **标签关联**: 创建商品时可以同时关联标签
6. **状态控制**: 建议创建时设为下架状态，配置完成后再上架

## 调用示例
```bash
curl -X POST http://localhost:8888/mulandoGreateDestiny/v1/admin/goods/create \
  -H "Content-Type: application/json" \
  -H "vinehoo-uid: 1" \
  -H "vinehoo-vos-name: YWRtaW4=" \
  -d '{
    "title": "精美礼品盒",
    "type": 1,
    "items_info": "[{\"name\":\"商品A\",\"short_code\":\"A001\",\"num\":1}]",
    "price": 99.99,
    "inventory": 100,
    "onsale_status": 3
  }'
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 100001 | 服务器错误 |
| 100002 | 参数错误 |
| 100003 | 用户不存在 |
| 100005 | 数据库错误 |
| 100008 | 数据已存在 |

## 注意事项
1. 创建成功后会自动设置创建时间和更新时间
2. items_info必须是有效的JSON格式
3. 图片URL需要是可访问的完整地址
4. 标签关联会同时创建到vh_goods_label表
5. 建议创建时先设为下架状态，配置完成后再上架
